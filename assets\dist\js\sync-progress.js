"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunknotion_to_wordpress"] = self["webpackChunknotion_to_wordpress"] || []).push([["sync-progress"],{

/***/ "./src/admin/components/SyncProgress.ts":
/*!**********************************************!*\
  !*** ./src/admin/components/SyncProgress.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SyncProgress: () => (/* binding */ SyncProgress),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n\n\n\n\n\n\n\n\n\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 同步进度组件\n */\n\n\nvar SyncProgress = /*#__PURE__*/function () {\n  function SyncProgress(selector) {\n    _classCallCheck(this, SyncProgress);\n    _defineProperty(this, \"element\", null);\n    _defineProperty(this, \"progressBar\", null);\n    _defineProperty(this, \"statusText\", null);\n    this.element = document.querySelector(selector);\n    if (this.element) {\n      this.init();\n    }\n  }\n  return _createClass(SyncProgress, [{\n    key: \"init\",\n    value: function init() {\n      if (!this.element) return;\n\n      // 创建进度条HTML结构\n      this.element.innerHTML = \"\\n      <div class=\\\"sync-progress\\\">\\n        <div class=\\\"progress-bar\\\">\\n          <div class=\\\"progress-fill\\\"></div>\\n        </div>\\n        <div class=\\\"status-text\\\">\\u51C6\\u5907\\u4E2D...</div>\\n      </div>\\n    \";\n      this.progressBar = this.element.querySelector('.progress-fill');\n      this.statusText = this.element.querySelector('.status-text');\n\n      // 监听同步事件\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.on('sync:start', this.onSyncStart.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.on('sync:progress', this.onSyncProgress.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.on('sync:complete', this.onSyncComplete.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.on('sync:error', this.onSyncError.bind(this));\n    }\n  }, {\n    key: \"onSyncStart\",\n    value: function onSyncStart() {\n      this.updateProgress(0, '开始同步...');\n    }\n  }, {\n    key: \"onSyncProgress\",\n    value: function onSyncProgress(_event, data) {\n      this.updateProgress(data.progress, data.message);\n    }\n  }, {\n    key: \"onSyncComplete\",\n    value: function onSyncComplete() {\n      this.updateProgress(100, '同步完成');\n    }\n  }, {\n    key: \"onSyncError\",\n    value: function onSyncError(_event, error) {\n      this.updateProgress(0, \"\\u540C\\u6B65\\u5931\\u8D25: \".concat(error.message));\n    }\n  }, {\n    key: \"updateProgress\",\n    value: function updateProgress(progress, message) {\n      if (this.progressBar) {\n        this.progressBar.style.width = \"\".concat(progress, \"%\");\n      }\n      if (this.statusText) {\n        this.statusText.textContent = message;\n      }\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.off('sync:start', this.onSyncStart.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.off('sync:progress', this.onSyncProgress.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.off('sync:complete', this.onSyncComplete.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_10__.eventBus.off('sync:error', this.onSyncError.bind(this));\n    }\n  }]);\n}();\n\n// 自动初始化\ndocument.addEventListener('DOMContentLoaded', function () {\n  var progressElement = document.querySelector('.sync-progress-container');\n  if (progressElement) {\n    new SyncProgress('.sync-progress-container');\n  }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SyncProgress);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/admin/components/SyncProgress.ts\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common"], () => (__webpack_exec__("./src/admin/components/SyncProgress.ts")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);