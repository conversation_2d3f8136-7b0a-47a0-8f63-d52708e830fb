/**
 * 前端样式
 */

@import '../shared/variables';

// Notion块基础样式
.notion-block {
  margin-bottom: $spacing-4;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 段落块
.notion-paragraph {
  line-height: $line-height-relaxed;
  color: $text-primary;
}

// 标题块
.notion-heading {
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  margin-bottom: $spacing-3;
  color: $text-primary;

  &-1 {
    font-size: $font-size-3xl;
    margin-bottom: $spacing-4;
  }

  &-2 {
    font-size: $font-size-2xl;
    margin-bottom: $spacing-3;
  }

  &-3 {
    font-size: $font-size-xl;
    margin-bottom: $spacing-2;
  }
}

// 列表块
.notion-list {
  padding-left: $spacing-6;
  
  &-item {
    margin-bottom: $spacing-2;
    line-height: $line-height-relaxed;
  }

  &.bulleted {
    list-style-type: disc;
  }

  &.numbered {
    list-style-type: decimal;
  }

  &.todo {
    list-style: none;
    padding-left: 0;

    .notion-list-item {
      display: flex;
      align-items: flex-start;
      gap: $spacing-2;

      &::before {
        content: '☐';
        flex-shrink: 0;
        margin-top: 2px;
      }

      &.checked::before {
        content: '☑';
        color: $success-color;
      }
    }
  }
}

// 引用块
.notion-quote {
  border-left: 4px solid $border-color-dark;
  padding-left: $spacing-4;
  margin: $spacing-4 0;
  font-style: italic;
  color: $text-secondary;
  background: $bg-secondary;
  padding: $spacing-4;
  border-radius: $border-radius-base;
}

// 代码块
.notion-code {
  background: $gray-900;
  color: $white;
  padding: $spacing-4;
  border-radius: $border-radius-base;
  font-family: $font-family-mono;
  font-size: $font-size-sm;
  line-height: $line-height-relaxed;
  overflow-x: auto;
  margin: $spacing-4 0;

  &.inline {
    display: inline;
    padding: $spacing-1 $spacing-2;
    margin: 0;
    background: $gray-100;
    color: $text-primary;
    border-radius: $border-radius-sm;
  }
}

// 分割线
.notion-divider {
  border: none;
  border-top: 1px solid $border-color;
  margin: $spacing-6 0;
}

// 图片块
.notion-image {
  margin: $spacing-4 0;
  text-align: center;

  img {
    max-width: 100%;
    height: auto;
    border-radius: $border-radius-base;
    box-shadow: $shadow-sm;
    transition: transform $transition-base;

    &:hover {
      transform: scale(1.02);
    }

    &[data-src] {
      opacity: 0.5;
      filter: blur(2px);
      transition: all $transition-base;

      &.loaded {
        opacity: 1;
        filter: none;
      }
    }
  }

  .caption {
    margin-top: $spacing-2;
    font-size: $font-size-sm;
    color: $text-muted;
    font-style: italic;
  }
}

// 视频块
.notion-video {
  margin: $spacing-4 0;
  position: relative;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  height: 0;
  overflow: hidden;
  border-radius: $border-radius-base;

  iframe,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
  }
}

// 书签块
.notion-bookmark {
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  padding: $spacing-4;
  margin: $spacing-4 0;
  display: flex;
  gap: $spacing-4;
  text-decoration: none;
  color: inherit;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-color;
    box-shadow: $shadow-sm;
    transform: translateY(-1px);
  }

  &-content {
    flex: 1;

    .title {
      font-weight: $font-weight-semibold;
      margin-bottom: $spacing-1;
      color: $text-primary;
    }

    .description {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-bottom: $spacing-2;
      line-height: $line-height-normal;
    }

    .url {
      font-size: $font-size-xs;
      color: $text-muted;
      text-decoration: none;
    }
  }

  &-image {
    flex: 0 0 120px;
    height: 80px;
    border-radius: $border-radius-sm;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 标注块
.notion-callout {
  display: flex;
  gap: $spacing-3;
  padding: $spacing-4;
  border-radius: $border-radius-base;
  margin: $spacing-4 0;
  background: $bg-secondary;
  border-left: 4px solid $primary-color;

  &-icon {
    flex-shrink: 0;
    font-size: $font-size-lg;
  }

  &-content {
    flex: 1;
    line-height: $line-height-relaxed;
  }

  // 不同类型的标注
  &.info {
    background: rgba($info-color, 0.1);
    border-left-color: $info-color;
  }

  &.warning {
    background: rgba($warning-color, 0.1);
    border-left-color: $warning-color;
  }

  &.error {
    background: rgba($error-color, 0.1);
    border-left-color: $error-color;
  }

  &.success {
    background: rgba($success-color, 0.1);
    border-left-color: $success-color;
  }
}

// 数学公式
.notion-equation {
  margin: $spacing-4 0;
  text-align: center;
  overflow-x: auto;

  &.inline {
    display: inline;
    margin: 0;
  }

  .katex {
    font-size: 1.1em;
  }
}

// 表格
.notion-table {
  width: 100%;
  border-collapse: collapse;
  margin: $spacing-4 0;
  border-radius: $border-radius-base;
  overflow: hidden;
  box-shadow: $shadow-sm;

  th,
  td {
    padding: $spacing-3;
    text-align: left;
    border-bottom: 1px solid $border-color;
  }

  th {
    background: $bg-secondary;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  tr:hover {
    background: $bg-secondary;
  }

  tr:last-child td {
    border-bottom: none;
  }
}

// 切换块
.notion-toggle {
  margin: $spacing-2 0;

  &-trigger {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    cursor: pointer;
    padding: $spacing-2;
    border-radius: $border-radius-sm;
    transition: background-color $transition-fast;

    &:hover {
      background: $bg-secondary;
    }

    .toggle-icon {
      transition: transform $transition-fast;
      
      &.expanded {
        transform: rotate(90deg);
      }
    }
  }

  &-content {
    padding-left: $spacing-6;
    margin-top: $spacing-2;
    display: none;

    &.expanded {
      display: block;
      animation: slideDown 0.3s ease-out;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .notion-heading {
    &-1 {
      font-size: $font-size-2xl;
    }

    &-2 {
      font-size: $font-size-xl;
    }

    &-3 {
      font-size: $font-size-lg;
    }
  }

  .notion-bookmark {
    flex-direction: column;

    &-image {
      flex: none;
      height: 200px;
    }
  }

  .notion-table {
    font-size: $font-size-sm;

    th,
    td {
      padding: $spacing-2;
    }
  }
}
