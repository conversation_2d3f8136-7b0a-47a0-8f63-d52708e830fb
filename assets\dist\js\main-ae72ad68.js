var ep=Object.defineProperty;var tp=(e,t,n)=>t in e?ep(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Po=(e,t,n)=>(tp(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const l of o.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function Yu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Zu={exports:{}},to={},ec={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ar=Symbol.for("react.element"),np=Symbol.for("react.portal"),rp=Symbol.for("react.fragment"),sp=Symbol.for("react.strict_mode"),op=Symbol.for("react.profiler"),lp=Symbol.for("react.provider"),ip=Symbol.for("react.context"),ap=Symbol.for("react.forward_ref"),up=Symbol.for("react.suspense"),cp=Symbol.for("react.memo"),dp=Symbol.for("react.lazy"),xa=Symbol.iterator;function fp(e){return e===null||typeof e!="object"?null:(e=xa&&e[xa]||e["@@iterator"],typeof e=="function"?e:null)}var tc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},nc=Object.assign,rc={};function Un(e,t,n){this.props=e,this.context=t,this.refs=rc,this.updater=n||tc}Un.prototype.isReactComponent={};Un.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Un.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function sc(){}sc.prototype=Un.prototype;function fi(e,t,n){this.props=e,this.context=t,this.refs=rc,this.updater=n||tc}var pi=fi.prototype=new sc;pi.constructor=fi;nc(pi,Un.prototype);pi.isPureReactComponent=!0;var wa=Array.isArray,oc=Object.prototype.hasOwnProperty,mi={current:null},lc={key:!0,ref:!0,__self:!0,__source:!0};function ic(e,t,n){var r,s={},o=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(o=""+t.key),t)oc.call(t,r)&&!lc.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:Ar,type:e,key:o,ref:l,props:s,_owner:mi.current}}function pp(e,t){return{$$typeof:Ar,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function hi(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ar}function mp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Sa=/\/+/g;function Ro(e,t){return typeof e=="object"&&e!==null&&e.key!=null?mp(""+e.key):t.toString(36)}function fs(e,t,n,r,s){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(o){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Ar:case np:l=!0}}if(l)return l=e,s=s(l),e=r===""?"."+Ro(l,0):r,wa(s)?(n="",e!=null&&(n=e.replace(Sa,"$&/")+"/"),fs(s,t,n,"",function(c){return c})):s!=null&&(hi(s)&&(s=pp(s,n+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(Sa,"$&/")+"/")+e)),t.push(s)),1;if(l=0,r=r===""?".":r+":",wa(e))for(var a=0;a<e.length;a++){o=e[a];var u=r+Ro(o,a);l+=fs(o,t,n,u,s)}else if(u=fp(e),typeof u=="function")for(e=u.call(e),a=0;!(o=e.next()).done;)o=o.value,u=r+Ro(o,a++),l+=fs(o,t,n,u,s);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function qr(e,t,n){if(e==null)return e;var r=[],s=0;return fs(e,r,"","",function(o){return t.call(n,o,s++)}),r}function hp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee={current:null},ps={transition:null},gp={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:ps,ReactCurrentOwner:mi};function ac(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:qr,forEach:function(e,t,n){qr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return qr(e,function(){t++}),t},toArray:function(e){return qr(e,function(t){return t})||[]},only:function(e){if(!hi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=Un;U.Fragment=rp;U.Profiler=op;U.PureComponent=fi;U.StrictMode=sp;U.Suspense=up;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gp;U.act=ac;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=nc({},e.props),s=e.key,o=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,l=mi.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)oc.call(t,u)&&!lc.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Ar,type:e.type,key:s,ref:o,props:r,_owner:l}};U.createContext=function(e){return e={$$typeof:ip,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:lp,_context:e},e.Consumer=e};U.createElement=ic;U.createFactory=function(e){var t=ic.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:ap,render:e}};U.isValidElement=hi;U.lazy=function(e){return{$$typeof:dp,_payload:{_status:-1,_result:e},_init:hp}};U.memo=function(e,t){return{$$typeof:cp,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=ps.transition;ps.transition={};try{e()}finally{ps.transition=t}};U.unstable_act=ac;U.useCallback=function(e,t){return Ee.current.useCallback(e,t)};U.useContext=function(e){return Ee.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return Ee.current.useDeferredValue(e)};U.useEffect=function(e,t){return Ee.current.useEffect(e,t)};U.useId=function(){return Ee.current.useId()};U.useImperativeHandle=function(e,t,n){return Ee.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return Ee.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return Ee.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return Ee.current.useMemo(e,t)};U.useReducer=function(e,t,n){return Ee.current.useReducer(e,t,n)};U.useRef=function(e){return Ee.current.useRef(e)};U.useState=function(e){return Ee.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return Ee.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return Ee.current.useTransition()};U.version="18.3.1";ec.exports=U;var M=ec.exports;const uc=Yu(M);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp=M,vp=Symbol.for("react.element"),xp=Symbol.for("react.fragment"),wp=Object.prototype.hasOwnProperty,Sp=yp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,kp={key:!0,ref:!0,__self:!0,__source:!0};function cc(e,t,n){var r,s={},o=null,l=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)wp.call(t,r)&&!kp.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:vp,type:e,key:o,ref:l,props:s,_owner:Sp.current}}to.Fragment=xp;to.jsx=cc;to.jsxs=cc;Zu.exports=to;var i=Zu.exports,cl={},dc={exports:{}},Ue={},fc={exports:{}},pc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,z){var D=j.length;j.push(z);e:for(;0<D;){var J=D-1>>>1,ne=j[J];if(0<s(ne,z))j[J]=z,j[D]=ne,D=J;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var z=j[0],D=j.pop();if(D!==z){j[0]=D;e:for(var J=0,ne=j.length,le=ne>>>1;J<le;){var se=2*(J+1)-1,jt=j[se],st=se+1,Y=j[st];if(0>s(jt,D))st<ne&&0>s(Y,jt)?(j[J]=Y,j[st]=D,J=st):(j[J]=jt,j[se]=D,J=se);else if(st<ne&&0>s(Y,D))j[J]=Y,j[st]=D,J=st;else break e}}return z}function s(j,z){var D=j.sortIndex-z.sortIndex;return D!==0?D:j.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var l=Date,a=l.now();e.unstable_now=function(){return l.now()-a}}var u=[],c=[],f=1,m=null,g=3,x=!1,y=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(j){for(var z=n(c);z!==null;){if(z.callback===null)r(c);else if(z.startTime<=j)r(c),z.sortIndex=z.expirationTime,t(u,z);else break;z=n(c)}}function k(j){if(v=!1,h(j),!y)if(n(u)!==null)y=!0,Ie(E);else{var z=n(c);z!==null&&Ge(k,z.startTime-j)}}function E(j,z){y=!1,v&&(v=!1,p(N),N=-1),x=!0;var D=g;try{for(h(z),m=n(u);m!==null&&(!(m.expirationTime>z)||j&&!ue());){var J=m.callback;if(typeof J=="function"){m.callback=null,g=m.priorityLevel;var ne=J(m.expirationTime<=z);z=e.unstable_now(),typeof ne=="function"?m.callback=ne:m===n(u)&&r(u),h(z)}else r(u);m=n(u)}if(m!==null)var le=!0;else{var se=n(c);se!==null&&Ge(k,se.startTime-z),le=!1}return le}finally{m=null,g=D,x=!1}}var b=!1,P=null,N=-1,C=5,O=-1;function ue(){return!(e.unstable_now()-O<C)}function ve(){if(P!==null){var j=e.unstable_now();O=j;var z=!0;try{z=P(!0,j)}finally{z?ze():(b=!1,P=null)}}else b=!1}var ze;if(typeof d=="function")ze=function(){d(ve)};else if(typeof MessageChannel<"u"){var dn=new MessageChannel,kt=dn.port2;dn.port1.onmessage=ve,ze=function(){kt.postMessage(null)}}else ze=function(){w(ve,0)};function Ie(j){P=j,b||(b=!0,ze())}function Ge(j,z){N=w(function(){j(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){y||x||(y=!0,Ie(E))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(j){switch(g){case 1:case 2:case 3:var z=3;break;default:z=g}var D=g;g=z;try{return j()}finally{g=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,z){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var D=g;g=j;try{return z()}finally{g=D}},e.unstable_scheduleCallback=function(j,z,D){var J=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?J+D:J):D=J,j){case 1:var ne=-1;break;case 2:ne=250;break;case 5:ne=**********;break;case 4:ne=1e4;break;default:ne=5e3}return ne=D+ne,j={id:f++,callback:z,priorityLevel:j,startTime:D,expirationTime:ne,sortIndex:-1},D>J?(j.sortIndex=D,t(c,j),n(u)===null&&j===n(c)&&(v?(p(N),N=-1):v=!0,Ge(k,D-J))):(j.sortIndex=ne,t(u,j),y||x||(y=!0,Ie(E))),j},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(j){var z=g;return function(){var D=g;g=z;try{return j.apply(this,arguments)}finally{g=D}}}})(pc);fc.exports=pc;var jp=fc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Np=M,Fe=jp;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var mc=new Set,yr={};function un(e,t){Ln(e,t),Ln(e+"Capture",t)}function Ln(e,t){for(yr[e]=t,e=0;e<t.length;e++)mc.add(t[e])}var yt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),dl=Object.prototype.hasOwnProperty,_p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ka={},ja={};function Ep(e){return dl.call(ja,e)?!0:dl.call(ka,e)?!1:_p.test(e)?ja[e]=!0:(ka[e]=!0,!1)}function bp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Cp(e,t,n,r){if(t===null||typeof t>"u"||bp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function be(e,t,n,r,s,o,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new be(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new be(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new be(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new be(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new be(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new be(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new be(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new be(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new be(e,5,!1,e.toLowerCase(),null,!1,!1)});var gi=/[\-:]([a-z])/g;function yi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(gi,yi);ye[t]=new be(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(gi,yi);ye[t]=new be(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(gi,yi);ye[t]=new be(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new be(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new be("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new be(e,1,!1,e.toLowerCase(),null,!0,!0)});function vi(e,t,n,r){var s=ye.hasOwnProperty(t)?ye[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Cp(t,n,s,r)&&(n=null),r||s===null?Ep(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var St=Np.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Gr=Symbol.for("react.element"),mn=Symbol.for("react.portal"),hn=Symbol.for("react.fragment"),xi=Symbol.for("react.strict_mode"),fl=Symbol.for("react.profiler"),hc=Symbol.for("react.provider"),gc=Symbol.for("react.context"),wi=Symbol.for("react.forward_ref"),pl=Symbol.for("react.suspense"),ml=Symbol.for("react.suspense_list"),Si=Symbol.for("react.memo"),Et=Symbol.for("react.lazy"),yc=Symbol.for("react.offscreen"),Na=Symbol.iterator;function Gn(e){return e===null||typeof e!="object"?null:(e=Na&&e[Na]||e["@@iterator"],typeof e=="function"?e:null)}var te=Object.assign,To;function sr(e){if(To===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);To=t&&t[1]||""}return`
`+To+e}var Lo=!1;function Oo(e,t){if(!e||Lo)return"";Lo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),o=r.stack.split(`
`),l=s.length-1,a=o.length-1;1<=l&&0<=a&&s[l]!==o[a];)a--;for(;1<=l&&0<=a;l--,a--)if(s[l]!==o[a]){if(l!==1||a!==1)do if(l--,a--,0>a||s[l]!==o[a]){var u=`
`+s[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=l&&0<=a);break}}}finally{Lo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?sr(e):""}function Pp(e){switch(e.tag){case 5:return sr(e.type);case 16:return sr("Lazy");case 13:return sr("Suspense");case 19:return sr("SuspenseList");case 0:case 2:case 15:return e=Oo(e.type,!1),e;case 11:return e=Oo(e.type.render,!1),e;case 1:return e=Oo(e.type,!0),e;default:return""}}function hl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case hn:return"Fragment";case mn:return"Portal";case fl:return"Profiler";case xi:return"StrictMode";case pl:return"Suspense";case ml:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case gc:return(e.displayName||"Context")+".Consumer";case hc:return(e._context.displayName||"Context")+".Provider";case wi:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Si:return t=e.displayName||null,t!==null?t:hl(e.type)||"Memo";case Et:t=e._payload,e=e._init;try{return hl(e(t))}catch{}}return null}function Rp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return hl(t);case 8:return t===xi?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Tp(e){var t=vc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(l){r=""+l,o.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Kr(e){e._valueTracker||(e._valueTracker=Tp(e))}function xc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=vc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Cs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function gl(e,t){var n=t.checked;return te({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function _a(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function wc(e,t){t=t.checked,t!=null&&vi(e,"checked",t,!1)}function yl(e,t){wc(e,t);var n=Bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vl(e,t.type,n):t.hasOwnProperty("defaultValue")&&vl(e,t.type,Bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ea(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vl(e,t,n){(t!=="number"||Cs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var or=Array.isArray;function En(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Bt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function xl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return te({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ba(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(or(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Bt(n)}}function Sc(e,t){var n=Bt(t.value),r=Bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ca(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function kc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function wl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?kc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Jr,jc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Jr=Jr||document.createElement("div"),Jr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Jr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function vr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ar={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Lp=["Webkit","ms","Moz","O"];Object.keys(ar).forEach(function(e){Lp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ar[t]=ar[e]})});function Nc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ar.hasOwnProperty(e)&&ar[e]?(""+t).trim():t+"px"}function _c(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Nc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Op=te({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Sl(e,t){if(t){if(Op[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function kl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jl=null;function ki(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nl=null,bn=null,Cn=null;function Pa(e){if(e=Fr(e)){if(typeof Nl!="function")throw Error(_(280));var t=e.stateNode;t&&(t=lo(t),Nl(e.stateNode,e.type,t))}}function Ec(e){bn?Cn?Cn.push(e):Cn=[e]:bn=e}function bc(){if(bn){var e=bn,t=Cn;if(Cn=bn=null,Pa(e),t)for(e=0;e<t.length;e++)Pa(t[e])}}function Cc(e,t){return e(t)}function Pc(){}var zo=!1;function Rc(e,t,n){if(zo)return e(t,n);zo=!0;try{return Cc(e,t,n)}finally{zo=!1,(bn!==null||Cn!==null)&&(Pc(),bc())}}function xr(e,t){var n=e.stateNode;if(n===null)return null;var r=lo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var _l=!1;if(yt)try{var Kn={};Object.defineProperty(Kn,"passive",{get:function(){_l=!0}}),window.addEventListener("test",Kn,Kn),window.removeEventListener("test",Kn,Kn)}catch{_l=!1}function zp(e,t,n,r,s,o,l,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var ur=!1,Ps=null,Rs=!1,El=null,Ip={onError:function(e){ur=!0,Ps=e}};function Ap(e,t,n,r,s,o,l,a,u){ur=!1,Ps=null,zp.apply(Ip,arguments)}function Mp(e,t,n,r,s,o,l,a,u){if(Ap.apply(this,arguments),ur){if(ur){var c=Ps;ur=!1,Ps=null}else throw Error(_(198));Rs||(Rs=!0,El=c)}}function cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Tc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ra(e){if(cn(e)!==e)throw Error(_(188))}function Dp(e){var t=e.alternate;if(!t){if(t=cn(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return Ra(s),e;if(o===r)return Ra(s),t;o=o.sibling}throw Error(_(188))}if(n.return!==r.return)n=s,r=o;else{for(var l=!1,a=s.child;a;){if(a===n){l=!0,n=s,r=o;break}if(a===r){l=!0,r=s,n=o;break}a=a.sibling}if(!l){for(a=o.child;a;){if(a===n){l=!0,n=o,r=s;break}if(a===r){l=!0,r=o,n=s;break}a=a.sibling}if(!l)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Lc(e){return e=Dp(e),e!==null?Oc(e):null}function Oc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Oc(e);if(t!==null)return t;e=e.sibling}return null}var zc=Fe.unstable_scheduleCallback,Ta=Fe.unstable_cancelCallback,Fp=Fe.unstable_shouldYield,Up=Fe.unstable_requestPaint,oe=Fe.unstable_now,Bp=Fe.unstable_getCurrentPriorityLevel,ji=Fe.unstable_ImmediatePriority,Ic=Fe.unstable_UserBlockingPriority,Ts=Fe.unstable_NormalPriority,$p=Fe.unstable_LowPriority,Ac=Fe.unstable_IdlePriority,no=null,ut=null;function Hp(e){if(ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(no,e,void 0,(e.current.flags&128)===128)}catch{}}var Ze=Math.clz32?Math.clz32:Qp,Vp=Math.log,Wp=Math.LN2;function Qp(e){return e>>>=0,e===0?32:31-(Vp(e)/Wp|0)|0}var Xr=64,Yr=4194304;function lr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ls(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,o=e.pingedLanes,l=n&268435455;if(l!==0){var a=l&~s;a!==0?r=lr(a):(o&=l,o!==0&&(r=lr(o)))}else l=n&~s,l!==0?r=lr(l):o!==0&&(r=lr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,o=t&-t,s>=o||s===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ze(t),s=1<<n,r|=e[n],t&=~s;return r}function qp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-Ze(o),a=1<<l,u=s[l];u===-1?(!(a&n)||a&r)&&(s[l]=qp(a,t)):u<=t&&(e.expiredLanes|=a),o&=~a}}function bl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Mc(){var e=Xr;return Xr<<=1,!(Xr&4194240)&&(Xr=64),e}function Io(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Mr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ze(t),e[t]=n}function Kp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Ze(n),o=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~o}}function Ni(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ze(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var V=0;function Dc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Fc,_i,Uc,Bc,$c,Cl=!1,Zr=[],Ot=null,zt=null,It=null,wr=new Map,Sr=new Map,Ct=[],Jp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function La(e,t){switch(e){case"focusin":case"focusout":Ot=null;break;case"dragenter":case"dragleave":zt=null;break;case"mouseover":case"mouseout":It=null;break;case"pointerover":case"pointerout":wr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Sr.delete(t.pointerId)}}function Jn(e,t,n,r,s,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[s]},t!==null&&(t=Fr(t),t!==null&&_i(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Xp(e,t,n,r,s){switch(t){case"focusin":return Ot=Jn(Ot,e,t,n,r,s),!0;case"dragenter":return zt=Jn(zt,e,t,n,r,s),!0;case"mouseover":return It=Jn(It,e,t,n,r,s),!0;case"pointerover":var o=s.pointerId;return wr.set(o,Jn(wr.get(o)||null,e,t,n,r,s)),!0;case"gotpointercapture":return o=s.pointerId,Sr.set(o,Jn(Sr.get(o)||null,e,t,n,r,s)),!0}return!1}function Hc(e){var t=Kt(e.target);if(t!==null){var n=cn(t);if(n!==null){if(t=n.tag,t===13){if(t=Tc(n),t!==null){e.blockedOn=t,$c(e.priority,function(){Uc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ms(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Pl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);jl=r,n.target.dispatchEvent(r),jl=null}else return t=Fr(n),t!==null&&_i(t),e.blockedOn=n,!1;t.shift()}return!0}function Oa(e,t,n){ms(e)&&n.delete(t)}function Yp(){Cl=!1,Ot!==null&&ms(Ot)&&(Ot=null),zt!==null&&ms(zt)&&(zt=null),It!==null&&ms(It)&&(It=null),wr.forEach(Oa),Sr.forEach(Oa)}function Xn(e,t){e.blockedOn===t&&(e.blockedOn=null,Cl||(Cl=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,Yp)))}function kr(e){function t(s){return Xn(s,e)}if(0<Zr.length){Xn(Zr[0],e);for(var n=1;n<Zr.length;n++){var r=Zr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ot!==null&&Xn(Ot,e),zt!==null&&Xn(zt,e),It!==null&&Xn(It,e),wr.forEach(t),Sr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)Hc(n),n.blockedOn===null&&Ct.shift()}var Pn=St.ReactCurrentBatchConfig,Os=!0;function Zp(e,t,n,r){var s=V,o=Pn.transition;Pn.transition=null;try{V=1,Ei(e,t,n,r)}finally{V=s,Pn.transition=o}}function em(e,t,n,r){var s=V,o=Pn.transition;Pn.transition=null;try{V=4,Ei(e,t,n,r)}finally{V=s,Pn.transition=o}}function Ei(e,t,n,r){if(Os){var s=Pl(e,t,n,r);if(s===null)Wo(e,t,r,zs,n),La(e,r);else if(Xp(s,e,t,n,r))r.stopPropagation();else if(La(e,r),t&4&&-1<Jp.indexOf(e)){for(;s!==null;){var o=Fr(s);if(o!==null&&Fc(o),o=Pl(e,t,n,r),o===null&&Wo(e,t,r,zs,n),o===s)break;s=o}s!==null&&r.stopPropagation()}else Wo(e,t,r,null,n)}}var zs=null;function Pl(e,t,n,r){if(zs=null,e=ki(r),e=Kt(e),e!==null)if(t=cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Tc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return zs=e,null}function Vc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Bp()){case ji:return 1;case Ic:return 4;case Ts:case $p:return 16;case Ac:return 536870912;default:return 16}default:return 16}}var Rt=null,bi=null,hs=null;function Wc(){if(hs)return hs;var e,t=bi,n=t.length,r,s="value"in Rt?Rt.value:Rt.textContent,o=s.length;for(e=0;e<n&&t[e]===s[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===s[o-r];r++);return hs=s.slice(e,1<r?1-r:void 0)}function gs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function es(){return!0}function za(){return!1}function Be(e){function t(n,r,s,o,l){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=o,this.target=l,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?es:za,this.isPropagationStopped=za,this}return te(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=es)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=es)},persist:function(){},isPersistent:es}),t}var Bn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ci=Be(Bn),Dr=te({},Bn,{view:0,detail:0}),tm=Be(Dr),Ao,Mo,Yn,ro=te({},Dr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Pi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yn&&(Yn&&e.type==="mousemove"?(Ao=e.screenX-Yn.screenX,Mo=e.screenY-Yn.screenY):Mo=Ao=0,Yn=e),Ao)},movementY:function(e){return"movementY"in e?e.movementY:Mo}}),Ia=Be(ro),nm=te({},ro,{dataTransfer:0}),rm=Be(nm),sm=te({},Dr,{relatedTarget:0}),Do=Be(sm),om=te({},Bn,{animationName:0,elapsedTime:0,pseudoElement:0}),lm=Be(om),im=te({},Bn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),am=Be(im),um=te({},Bn,{data:0}),Aa=Be(um),cm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=fm[e])?!!t[e]:!1}function Pi(){return pm}var mm=te({},Dr,{key:function(e){if(e.key){var t=cm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=gs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Pi,charCode:function(e){return e.type==="keypress"?gs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?gs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),hm=Be(mm),gm=te({},ro,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ma=Be(gm),ym=te({},Dr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Pi}),vm=Be(ym),xm=te({},Bn,{propertyName:0,elapsedTime:0,pseudoElement:0}),wm=Be(xm),Sm=te({},ro,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),km=Be(Sm),jm=[9,13,27,32],Ri=yt&&"CompositionEvent"in window,cr=null;yt&&"documentMode"in document&&(cr=document.documentMode);var Nm=yt&&"TextEvent"in window&&!cr,Qc=yt&&(!Ri||cr&&8<cr&&11>=cr),Da=String.fromCharCode(32),Fa=!1;function qc(e,t){switch(e){case"keyup":return jm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var gn=!1;function _m(e,t){switch(e){case"compositionend":return Gc(t);case"keypress":return t.which!==32?null:(Fa=!0,Da);case"textInput":return e=t.data,e===Da&&Fa?null:e;default:return null}}function Em(e,t){if(gn)return e==="compositionend"||!Ri&&qc(e,t)?(e=Wc(),hs=bi=Rt=null,gn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qc&&t.locale!=="ko"?null:t.data;default:return null}}var bm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ua(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!bm[e.type]:t==="textarea"}function Kc(e,t,n,r){Ec(r),t=Is(t,"onChange"),0<t.length&&(n=new Ci("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var dr=null,jr=null;function Cm(e){ld(e,0)}function so(e){var t=xn(e);if(xc(t))return e}function Pm(e,t){if(e==="change")return t}var Jc=!1;if(yt){var Fo;if(yt){var Uo="oninput"in document;if(!Uo){var Ba=document.createElement("div");Ba.setAttribute("oninput","return;"),Uo=typeof Ba.oninput=="function"}Fo=Uo}else Fo=!1;Jc=Fo&&(!document.documentMode||9<document.documentMode)}function $a(){dr&&(dr.detachEvent("onpropertychange",Xc),jr=dr=null)}function Xc(e){if(e.propertyName==="value"&&so(jr)){var t=[];Kc(t,jr,e,ki(e)),Rc(Cm,t)}}function Rm(e,t,n){e==="focusin"?($a(),dr=t,jr=n,dr.attachEvent("onpropertychange",Xc)):e==="focusout"&&$a()}function Tm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return so(jr)}function Lm(e,t){if(e==="click")return so(t)}function Om(e,t){if(e==="input"||e==="change")return so(t)}function zm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var nt=typeof Object.is=="function"?Object.is:zm;function Nr(e,t){if(nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!dl.call(t,s)||!nt(e[s],t[s]))return!1}return!0}function Ha(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Va(e,t){var n=Ha(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ha(n)}}function Yc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Yc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Zc(){for(var e=window,t=Cs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Cs(e.document)}return t}function Ti(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Im(e){var t=Zc(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Yc(n.ownerDocument.documentElement,n)){if(r!==null&&Ti(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,o=Math.min(r.start,s);r=r.end===void 0?o:Math.min(r.end,s),!e.extend&&o>r&&(s=r,r=o,o=s),s=Va(n,o);var l=Va(n,r);s&&l&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Am=yt&&"documentMode"in document&&11>=document.documentMode,yn=null,Rl=null,fr=null,Tl=!1;function Wa(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Tl||yn==null||yn!==Cs(r)||(r=yn,"selectionStart"in r&&Ti(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),fr&&Nr(fr,r)||(fr=r,r=Is(Rl,"onSelect"),0<r.length&&(t=new Ci("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yn)))}function ts(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var vn={animationend:ts("Animation","AnimationEnd"),animationiteration:ts("Animation","AnimationIteration"),animationstart:ts("Animation","AnimationStart"),transitionend:ts("Transition","TransitionEnd")},Bo={},ed={};yt&&(ed=document.createElement("div").style,"AnimationEvent"in window||(delete vn.animationend.animation,delete vn.animationiteration.animation,delete vn.animationstart.animation),"TransitionEvent"in window||delete vn.transitionend.transition);function oo(e){if(Bo[e])return Bo[e];if(!vn[e])return e;var t=vn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ed)return Bo[e]=t[n];return e}var td=oo("animationend"),nd=oo("animationiteration"),rd=oo("animationstart"),sd=oo("transitionend"),od=new Map,Qa="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ht(e,t){od.set(e,t),un(t,[e])}for(var $o=0;$o<Qa.length;$o++){var Ho=Qa[$o],Mm=Ho.toLowerCase(),Dm=Ho[0].toUpperCase()+Ho.slice(1);Ht(Mm,"on"+Dm)}Ht(td,"onAnimationEnd");Ht(nd,"onAnimationIteration");Ht(rd,"onAnimationStart");Ht("dblclick","onDoubleClick");Ht("focusin","onFocus");Ht("focusout","onBlur");Ht(sd,"onTransitionEnd");Ln("onMouseEnter",["mouseout","mouseover"]);Ln("onMouseLeave",["mouseout","mouseover"]);Ln("onPointerEnter",["pointerout","pointerover"]);Ln("onPointerLeave",["pointerout","pointerover"]);un("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));un("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));un("onBeforeInput",["compositionend","keypress","textInput","paste"]);un("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));un("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));un("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ir="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fm=new Set("cancel close invalid load scroll toggle".split(" ").concat(ir));function qa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Mp(r,t,void 0,e),e.currentTarget=null}function ld(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var a=r[l],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==o&&s.isPropagationStopped())break e;qa(s,a,c),o=u}else for(l=0;l<r.length;l++){if(a=r[l],u=a.instance,c=a.currentTarget,a=a.listener,u!==o&&s.isPropagationStopped())break e;qa(s,a,c),o=u}}}if(Rs)throw e=El,Rs=!1,El=null,e}function Q(e,t){var n=t[Al];n===void 0&&(n=t[Al]=new Set);var r=e+"__bubble";n.has(r)||(id(t,e,2,!1),n.add(r))}function Vo(e,t,n){var r=0;t&&(r|=4),id(n,e,r,t)}var ns="_reactListening"+Math.random().toString(36).slice(2);function _r(e){if(!e[ns]){e[ns]=!0,mc.forEach(function(n){n!=="selectionchange"&&(Fm.has(n)||Vo(n,!1,e),Vo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ns]||(t[ns]=!0,Vo("selectionchange",!1,t))}}function id(e,t,n,r){switch(Vc(t)){case 1:var s=Zp;break;case 4:s=em;break;default:s=Ei}n=s.bind(null,t,n,e),s=void 0,!_l||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Wo(e,t,n,r,s){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(l===4)for(l=r.return;l!==null;){var u=l.tag;if((u===3||u===4)&&(u=l.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;l=l.return}for(;a!==null;){if(l=Kt(a),l===null)return;if(u=l.tag,u===5||u===6){r=o=l;continue e}a=a.parentNode}}r=r.return}Rc(function(){var c=o,f=ki(n),m=[];e:{var g=od.get(e);if(g!==void 0){var x=Ci,y=e;switch(e){case"keypress":if(gs(n)===0)break e;case"keydown":case"keyup":x=hm;break;case"focusin":y="focus",x=Do;break;case"focusout":y="blur",x=Do;break;case"beforeblur":case"afterblur":x=Do;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=Ia;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=rm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=vm;break;case td:case nd:case rd:x=lm;break;case sd:x=wm;break;case"scroll":x=tm;break;case"wheel":x=km;break;case"copy":case"cut":case"paste":x=am;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=Ma}var v=(t&4)!==0,w=!v&&e==="scroll",p=v?g!==null?g+"Capture":null:g;v=[];for(var d=c,h;d!==null;){h=d;var k=h.stateNode;if(h.tag===5&&k!==null&&(h=k,p!==null&&(k=xr(d,p),k!=null&&v.push(Er(d,k,h)))),w)break;d=d.return}0<v.length&&(g=new x(g,y,null,n,f),m.push({event:g,listeners:v}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",g&&n!==jl&&(y=n.relatedTarget||n.fromElement)&&(Kt(y)||y[vt]))break e;if((x||g)&&(g=f.window===f?f:(g=f.ownerDocument)?g.defaultView||g.parentWindow:window,x?(y=n.relatedTarget||n.toElement,x=c,y=y?Kt(y):null,y!==null&&(w=cn(y),y!==w||y.tag!==5&&y.tag!==6)&&(y=null)):(x=null,y=c),x!==y)){if(v=Ia,k="onMouseLeave",p="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(v=Ma,k="onPointerLeave",p="onPointerEnter",d="pointer"),w=x==null?g:xn(x),h=y==null?g:xn(y),g=new v(k,d+"leave",x,n,f),g.target=w,g.relatedTarget=h,k=null,Kt(f)===c&&(v=new v(p,d+"enter",y,n,f),v.target=h,v.relatedTarget=w,k=v),w=k,x&&y)t:{for(v=x,p=y,d=0,h=v;h;h=fn(h))d++;for(h=0,k=p;k;k=fn(k))h++;for(;0<d-h;)v=fn(v),d--;for(;0<h-d;)p=fn(p),h--;for(;d--;){if(v===p||p!==null&&v===p.alternate)break t;v=fn(v),p=fn(p)}v=null}else v=null;x!==null&&Ga(m,g,x,v,!1),y!==null&&w!==null&&Ga(m,w,y,v,!0)}}e:{if(g=c?xn(c):window,x=g.nodeName&&g.nodeName.toLowerCase(),x==="select"||x==="input"&&g.type==="file")var E=Pm;else if(Ua(g))if(Jc)E=Om;else{E=Tm;var b=Rm}else(x=g.nodeName)&&x.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(E=Lm);if(E&&(E=E(e,c))){Kc(m,E,n,f);break e}b&&b(e,g,c),e==="focusout"&&(b=g._wrapperState)&&b.controlled&&g.type==="number"&&vl(g,"number",g.value)}switch(b=c?xn(c):window,e){case"focusin":(Ua(b)||b.contentEditable==="true")&&(yn=b,Rl=c,fr=null);break;case"focusout":fr=Rl=yn=null;break;case"mousedown":Tl=!0;break;case"contextmenu":case"mouseup":case"dragend":Tl=!1,Wa(m,n,f);break;case"selectionchange":if(Am)break;case"keydown":case"keyup":Wa(m,n,f)}var P;if(Ri)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else gn?qc(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(Qc&&n.locale!=="ko"&&(gn||N!=="onCompositionStart"?N==="onCompositionEnd"&&gn&&(P=Wc()):(Rt=f,bi="value"in Rt?Rt.value:Rt.textContent,gn=!0)),b=Is(c,N),0<b.length&&(N=new Aa(N,e,null,n,f),m.push({event:N,listeners:b}),P?N.data=P:(P=Gc(n),P!==null&&(N.data=P)))),(P=Nm?_m(e,n):Em(e,n))&&(c=Is(c,"onBeforeInput"),0<c.length&&(f=new Aa("onBeforeInput","beforeinput",null,n,f),m.push({event:f,listeners:c}),f.data=P))}ld(m,t)})}function Er(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Is(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,o=s.stateNode;s.tag===5&&o!==null&&(s=o,o=xr(e,n),o!=null&&r.unshift(Er(e,o,s)),o=xr(e,t),o!=null&&r.push(Er(e,o,s))),e=e.return}return r}function fn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ga(e,t,n,r,s){for(var o=t._reactName,l=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,s?(u=xr(n,o),u!=null&&l.unshift(Er(n,u,a))):s||(u=xr(n,o),u!=null&&l.push(Er(n,u,a)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Um=/\r\n?/g,Bm=/\u0000|\uFFFD/g;function Ka(e){return(typeof e=="string"?e:""+e).replace(Um,`
`).replace(Bm,"")}function rs(e,t,n){if(t=Ka(t),Ka(e)!==t&&n)throw Error(_(425))}function As(){}var Ll=null,Ol=null;function zl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Il=typeof setTimeout=="function"?setTimeout:void 0,$m=typeof clearTimeout=="function"?clearTimeout:void 0,Ja=typeof Promise=="function"?Promise:void 0,Hm=typeof queueMicrotask=="function"?queueMicrotask:typeof Ja<"u"?function(e){return Ja.resolve(null).then(e).catch(Vm)}:Il;function Vm(e){setTimeout(function(){throw e})}function Qo(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),kr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);kr(t)}function At(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xa(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var $n=Math.random().toString(36).slice(2),at="__reactFiber$"+$n,br="__reactProps$"+$n,vt="__reactContainer$"+$n,Al="__reactEvents$"+$n,Wm="__reactListeners$"+$n,Qm="__reactHandles$"+$n;function Kt(e){var t=e[at];if(t)return t;for(var n=e.parentNode;n;){if(t=n[vt]||n[at]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xa(e);e!==null;){if(n=e[at])return n;e=Xa(e)}return t}e=n,n=e.parentNode}return null}function Fr(e){return e=e[at]||e[vt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function xn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function lo(e){return e[br]||null}var Ml=[],wn=-1;function Vt(e){return{current:e}}function G(e){0>wn||(e.current=Ml[wn],Ml[wn]=null,wn--)}function W(e,t){wn++,Ml[wn]=e.current,e.current=t}var $t={},je=Vt($t),Re=Vt(!1),nn=$t;function On(e,t){var n=e.type.contextTypes;if(!n)return $t;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},o;for(o in n)s[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Te(e){return e=e.childContextTypes,e!=null}function Ms(){G(Re),G(je)}function Ya(e,t,n){if(je.current!==$t)throw Error(_(168));W(je,t),W(Re,n)}function ad(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(_(108,Rp(e)||"Unknown",s));return te({},n,r)}function Ds(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||$t,nn=je.current,W(je,e),W(Re,Re.current),!0}function Za(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=ad(e,t,nn),r.__reactInternalMemoizedMergedChildContext=e,G(Re),G(je),W(je,e)):G(Re),W(Re,n)}var pt=null,io=!1,qo=!1;function ud(e){pt===null?pt=[e]:pt.push(e)}function qm(e){io=!0,ud(e)}function Wt(){if(!qo&&pt!==null){qo=!0;var e=0,t=V;try{var n=pt;for(V=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}pt=null,io=!1}catch(s){throw pt!==null&&(pt=pt.slice(e+1)),zc(ji,Wt),s}finally{V=t,qo=!1}}return null}var Sn=[],kn=0,Fs=null,Us=0,$e=[],He=0,rn=null,mt=1,ht="";function qt(e,t){Sn[kn++]=Us,Sn[kn++]=Fs,Fs=e,Us=t}function cd(e,t,n){$e[He++]=mt,$e[He++]=ht,$e[He++]=rn,rn=e;var r=mt;e=ht;var s=32-Ze(r)-1;r&=~(1<<s),n+=1;var o=32-Ze(t)+s;if(30<o){var l=s-s%5;o=(r&(1<<l)-1).toString(32),r>>=l,s-=l,mt=1<<32-Ze(t)+s|n<<s|r,ht=o+e}else mt=1<<o|n<<s|r,ht=e}function Li(e){e.return!==null&&(qt(e,1),cd(e,1,0))}function Oi(e){for(;e===Fs;)Fs=Sn[--kn],Sn[kn]=null,Us=Sn[--kn],Sn[kn]=null;for(;e===rn;)rn=$e[--He],$e[He]=null,ht=$e[--He],$e[He]=null,mt=$e[--He],$e[He]=null}var De=null,Me=null,X=!1,Ye=null;function dd(e,t){var n=Ve(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function eu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Me=At(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=rn!==null?{id:mt,overflow:ht}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ve(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Me=null,!0):!1;default:return!1}}function Dl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Fl(e){if(X){var t=Me;if(t){var n=t;if(!eu(e,t)){if(Dl(e))throw Error(_(418));t=At(n.nextSibling);var r=De;t&&eu(e,t)?dd(r,n):(e.flags=e.flags&-4097|2,X=!1,De=e)}}else{if(Dl(e))throw Error(_(418));e.flags=e.flags&-4097|2,X=!1,De=e}}}function tu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function ss(e){if(e!==De)return!1;if(!X)return tu(e),X=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!zl(e.type,e.memoizedProps)),t&&(t=Me)){if(Dl(e))throw fd(),Error(_(418));for(;t;)dd(e,t),t=At(t.nextSibling)}if(tu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=At(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=De?At(e.stateNode.nextSibling):null;return!0}function fd(){for(var e=Me;e;)e=At(e.nextSibling)}function zn(){Me=De=null,X=!1}function zi(e){Ye===null?Ye=[e]:Ye.push(e)}var Gm=St.ReactCurrentBatchConfig;function Zn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var s=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(l){var a=s.refs;l===null?delete a[o]:a[o]=l},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function os(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nu(e){var t=e._init;return t(e._payload)}function pd(e){function t(p,d){if(e){var h=p.deletions;h===null?(p.deletions=[d],p.flags|=16):h.push(d)}}function n(p,d){if(!e)return null;for(;d!==null;)t(p,d),d=d.sibling;return null}function r(p,d){for(p=new Map;d!==null;)d.key!==null?p.set(d.key,d):p.set(d.index,d),d=d.sibling;return p}function s(p,d){return p=Ut(p,d),p.index=0,p.sibling=null,p}function o(p,d,h){return p.index=h,e?(h=p.alternate,h!==null?(h=h.index,h<d?(p.flags|=2,d):h):(p.flags|=2,d)):(p.flags|=1048576,d)}function l(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,d,h,k){return d===null||d.tag!==6?(d=el(h,p.mode,k),d.return=p,d):(d=s(d,h),d.return=p,d)}function u(p,d,h,k){var E=h.type;return E===hn?f(p,d,h.props.children,k,h.key):d!==null&&(d.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Et&&nu(E)===d.type)?(k=s(d,h.props),k.ref=Zn(p,d,h),k.return=p,k):(k=js(h.type,h.key,h.props,null,p.mode,k),k.ref=Zn(p,d,h),k.return=p,k)}function c(p,d,h,k){return d===null||d.tag!==4||d.stateNode.containerInfo!==h.containerInfo||d.stateNode.implementation!==h.implementation?(d=tl(h,p.mode,k),d.return=p,d):(d=s(d,h.children||[]),d.return=p,d)}function f(p,d,h,k,E){return d===null||d.tag!==7?(d=tn(h,p.mode,k,E),d.return=p,d):(d=s(d,h),d.return=p,d)}function m(p,d,h){if(typeof d=="string"&&d!==""||typeof d=="number")return d=el(""+d,p.mode,h),d.return=p,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Gr:return h=js(d.type,d.key,d.props,null,p.mode,h),h.ref=Zn(p,null,d),h.return=p,h;case mn:return d=tl(d,p.mode,h),d.return=p,d;case Et:var k=d._init;return m(p,k(d._payload),h)}if(or(d)||Gn(d))return d=tn(d,p.mode,h,null),d.return=p,d;os(p,d)}return null}function g(p,d,h,k){var E=d!==null?d.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return E!==null?null:a(p,d,""+h,k);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Gr:return h.key===E?u(p,d,h,k):null;case mn:return h.key===E?c(p,d,h,k):null;case Et:return E=h._init,g(p,d,E(h._payload),k)}if(or(h)||Gn(h))return E!==null?null:f(p,d,h,k,null);os(p,h)}return null}function x(p,d,h,k,E){if(typeof k=="string"&&k!==""||typeof k=="number")return p=p.get(h)||null,a(d,p,""+k,E);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Gr:return p=p.get(k.key===null?h:k.key)||null,u(d,p,k,E);case mn:return p=p.get(k.key===null?h:k.key)||null,c(d,p,k,E);case Et:var b=k._init;return x(p,d,h,b(k._payload),E)}if(or(k)||Gn(k))return p=p.get(h)||null,f(d,p,k,E,null);os(d,k)}return null}function y(p,d,h,k){for(var E=null,b=null,P=d,N=d=0,C=null;P!==null&&N<h.length;N++){P.index>N?(C=P,P=null):C=P.sibling;var O=g(p,P,h[N],k);if(O===null){P===null&&(P=C);break}e&&P&&O.alternate===null&&t(p,P),d=o(O,d,N),b===null?E=O:b.sibling=O,b=O,P=C}if(N===h.length)return n(p,P),X&&qt(p,N),E;if(P===null){for(;N<h.length;N++)P=m(p,h[N],k),P!==null&&(d=o(P,d,N),b===null?E=P:b.sibling=P,b=P);return X&&qt(p,N),E}for(P=r(p,P);N<h.length;N++)C=x(P,p,N,h[N],k),C!==null&&(e&&C.alternate!==null&&P.delete(C.key===null?N:C.key),d=o(C,d,N),b===null?E=C:b.sibling=C,b=C);return e&&P.forEach(function(ue){return t(p,ue)}),X&&qt(p,N),E}function v(p,d,h,k){var E=Gn(h);if(typeof E!="function")throw Error(_(150));if(h=E.call(h),h==null)throw Error(_(151));for(var b=E=null,P=d,N=d=0,C=null,O=h.next();P!==null&&!O.done;N++,O=h.next()){P.index>N?(C=P,P=null):C=P.sibling;var ue=g(p,P,O.value,k);if(ue===null){P===null&&(P=C);break}e&&P&&ue.alternate===null&&t(p,P),d=o(ue,d,N),b===null?E=ue:b.sibling=ue,b=ue,P=C}if(O.done)return n(p,P),X&&qt(p,N),E;if(P===null){for(;!O.done;N++,O=h.next())O=m(p,O.value,k),O!==null&&(d=o(O,d,N),b===null?E=O:b.sibling=O,b=O);return X&&qt(p,N),E}for(P=r(p,P);!O.done;N++,O=h.next())O=x(P,p,N,O.value,k),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?N:O.key),d=o(O,d,N),b===null?E=O:b.sibling=O,b=O);return e&&P.forEach(function(ve){return t(p,ve)}),X&&qt(p,N),E}function w(p,d,h,k){if(typeof h=="object"&&h!==null&&h.type===hn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Gr:e:{for(var E=h.key,b=d;b!==null;){if(b.key===E){if(E=h.type,E===hn){if(b.tag===7){n(p,b.sibling),d=s(b,h.props.children),d.return=p,p=d;break e}}else if(b.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Et&&nu(E)===b.type){n(p,b.sibling),d=s(b,h.props),d.ref=Zn(p,b,h),d.return=p,p=d;break e}n(p,b);break}else t(p,b);b=b.sibling}h.type===hn?(d=tn(h.props.children,p.mode,k,h.key),d.return=p,p=d):(k=js(h.type,h.key,h.props,null,p.mode,k),k.ref=Zn(p,d,h),k.return=p,p=k)}return l(p);case mn:e:{for(b=h.key;d!==null;){if(d.key===b)if(d.tag===4&&d.stateNode.containerInfo===h.containerInfo&&d.stateNode.implementation===h.implementation){n(p,d.sibling),d=s(d,h.children||[]),d.return=p,p=d;break e}else{n(p,d);break}else t(p,d);d=d.sibling}d=tl(h,p.mode,k),d.return=p,p=d}return l(p);case Et:return b=h._init,w(p,d,b(h._payload),k)}if(or(h))return y(p,d,h,k);if(Gn(h))return v(p,d,h,k);os(p,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,d!==null&&d.tag===6?(n(p,d.sibling),d=s(d,h),d.return=p,p=d):(n(p,d),d=el(h,p.mode,k),d.return=p,p=d),l(p)):n(p,d)}return w}var In=pd(!0),md=pd(!1),Bs=Vt(null),$s=null,jn=null,Ii=null;function Ai(){Ii=jn=$s=null}function Mi(e){var t=Bs.current;G(Bs),e._currentValue=t}function Ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Rn(e,t){$s=e,Ii=jn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Pe=!0),e.firstContext=null)}function Qe(e){var t=e._currentValue;if(Ii!==e)if(e={context:e,memoizedValue:t,next:null},jn===null){if($s===null)throw Error(_(308));jn=e,$s.dependencies={lanes:0,firstContext:e}}else jn=jn.next=e;return t}var Jt=null;function Di(e){Jt===null?Jt=[e]:Jt.push(e)}function hd(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Di(t)):(n.next=s.next,s.next=n),t.interleaved=n,xt(e,r)}function xt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var bt=!1;function Fi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function gd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function gt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,xt(e,n)}return s=r.interleaved,s===null?(t.next=t,Di(r)):(t.next=s.next,s.next=t),r.interleaved=t,xt(e,n)}function ys(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ni(e,n)}}function ru(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?s=o=l:o=o.next=l,n=n.next}while(n!==null);o===null?s=o=t:o=o.next=t}else s=o=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Hs(e,t,n,r){var s=e.updateQueue;bt=!1;var o=s.firstBaseUpdate,l=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,l===null?o=c:l.next=c,l=u;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==l&&(a===null?f.firstBaseUpdate=c:a.next=c,f.lastBaseUpdate=u))}if(o!==null){var m=s.baseState;l=0,f=c=u=null,a=o;do{var g=a.lane,x=a.eventTime;if((r&g)===g){f!==null&&(f=f.next={eventTime:x,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,v=a;switch(g=t,x=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){m=y.call(x,m,g);break e}m=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,g=typeof y=="function"?y.call(x,m,g):y,g==null)break e;m=te({},m,g);break e;case 2:bt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,g=s.effects,g===null?s.effects=[a]:g.push(a))}else x={eventTime:x,lane:g,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(c=f=x,u=m):f=f.next=x,l|=g;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;g=a,a=g.next,g.next=null,s.lastBaseUpdate=g,s.shared.pending=null}}while(1);if(f===null&&(u=m),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=f,t=s.shared.interleaved,t!==null){s=t;do l|=s.lane,s=s.next;while(s!==t)}else o===null&&(s.shared.lanes=0);on|=l,e.lanes=l,e.memoizedState=m}}function su(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(_(191,s));s.call(r)}}}var Ur={},ct=Vt(Ur),Cr=Vt(Ur),Pr=Vt(Ur);function Xt(e){if(e===Ur)throw Error(_(174));return e}function Ui(e,t){switch(W(Pr,t),W(Cr,e),W(ct,Ur),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:wl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=wl(t,e)}G(ct),W(ct,t)}function An(){G(ct),G(Cr),G(Pr)}function yd(e){Xt(Pr.current);var t=Xt(ct.current),n=wl(t,e.type);t!==n&&(W(Cr,e),W(ct,n))}function Bi(e){Cr.current===e&&(G(ct),G(Cr))}var Z=Vt(0);function Vs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Go=[];function $i(){for(var e=0;e<Go.length;e++)Go[e]._workInProgressVersionPrimary=null;Go.length=0}var vs=St.ReactCurrentDispatcher,Ko=St.ReactCurrentBatchConfig,sn=0,ee=null,de=null,pe=null,Ws=!1,pr=!1,Rr=0,Km=0;function xe(){throw Error(_(321))}function Hi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!nt(e[n],t[n]))return!1;return!0}function Vi(e,t,n,r,s,o){if(sn=o,ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,vs.current=e===null||e.memoizedState===null?Zm:eh,e=n(r,s),pr){o=0;do{if(pr=!1,Rr=0,25<=o)throw Error(_(301));o+=1,pe=de=null,t.updateQueue=null,vs.current=th,e=n(r,s)}while(pr)}if(vs.current=Qs,t=de!==null&&de.next!==null,sn=0,pe=de=ee=null,Ws=!1,t)throw Error(_(300));return e}function Wi(){var e=Rr!==0;return Rr=0,e}function it(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return pe===null?ee.memoizedState=pe=e:pe=pe.next=e,pe}function qe(){if(de===null){var e=ee.alternate;e=e!==null?e.memoizedState:null}else e=de.next;var t=pe===null?ee.memoizedState:pe.next;if(t!==null)pe=t,de=e;else{if(e===null)throw Error(_(310));de=e,e={memoizedState:de.memoizedState,baseState:de.baseState,baseQueue:de.baseQueue,queue:de.queue,next:null},pe===null?ee.memoizedState=pe=e:pe=pe.next=e}return pe}function Tr(e,t){return typeof t=="function"?t(e):t}function Jo(e){var t=qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=de,s=r.baseQueue,o=n.pending;if(o!==null){if(s!==null){var l=s.next;s.next=o.next,o.next=l}r.baseQueue=s=o,n.pending=null}if(s!==null){o=s.next,r=r.baseState;var a=l=null,u=null,c=o;do{var f=c.lane;if((sn&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=m,l=r):u=u.next=m,ee.lanes|=f,on|=f}c=c.next}while(c!==null&&c!==o);u===null?l=r:u.next=a,nt(r,t.memoizedState)||(Pe=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do o=s.lane,ee.lanes|=o,on|=o,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Xo(e){var t=qe(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,o=t.memoizedState;if(s!==null){n.pending=null;var l=s=s.next;do o=e(o,l.action),l=l.next;while(l!==s);nt(o,t.memoizedState)||(Pe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function vd(){}function xd(e,t){var n=ee,r=qe(),s=t(),o=!nt(r.memoizedState,s);if(o&&(r.memoizedState=s,Pe=!0),r=r.queue,Qi(kd.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||pe!==null&&pe.memoizedState.tag&1){if(n.flags|=2048,Lr(9,Sd.bind(null,n,r,s,t),void 0,null),me===null)throw Error(_(349));sn&30||wd(n,t,s)}return s}function wd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ee.updateQueue,t===null?(t={lastEffect:null,stores:null},ee.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Sd(e,t,n,r){t.value=n,t.getSnapshot=r,jd(t)&&Nd(e)}function kd(e,t,n){return n(function(){jd(t)&&Nd(e)})}function jd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!nt(e,n)}catch{return!0}}function Nd(e){var t=xt(e,1);t!==null&&et(t,e,1,-1)}function ou(e){var t=it();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Tr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ym.bind(null,ee,e),[t.memoizedState,e]}function Lr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ee.updateQueue,t===null?(t={lastEffect:null,stores:null},ee.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function _d(){return qe().memoizedState}function xs(e,t,n,r){var s=it();ee.flags|=e,s.memoizedState=Lr(1|t,n,void 0,r===void 0?null:r)}function ao(e,t,n,r){var s=qe();r=r===void 0?null:r;var o=void 0;if(de!==null){var l=de.memoizedState;if(o=l.destroy,r!==null&&Hi(r,l.deps)){s.memoizedState=Lr(t,n,o,r);return}}ee.flags|=e,s.memoizedState=Lr(1|t,n,o,r)}function lu(e,t){return xs(8390656,8,e,t)}function Qi(e,t){return ao(2048,8,e,t)}function Ed(e,t){return ao(4,2,e,t)}function bd(e,t){return ao(4,4,e,t)}function Cd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Pd(e,t,n){return n=n!=null?n.concat([e]):null,ao(4,4,Cd.bind(null,t,e),n)}function qi(){}function Rd(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Hi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Td(e,t){var n=qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Hi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ld(e,t,n){return sn&21?(nt(n,t)||(n=Mc(),ee.lanes|=n,on|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Pe=!0),e.memoizedState=n)}function Jm(e,t){var n=V;V=n!==0&&4>n?n:4,e(!0);var r=Ko.transition;Ko.transition={};try{e(!1),t()}finally{V=n,Ko.transition=r}}function Od(){return qe().memoizedState}function Xm(e,t,n){var r=Ft(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},zd(e))Id(t,n);else if(n=hd(e,t,n,r),n!==null){var s=_e();et(n,e,r,s),Ad(n,t,r)}}function Ym(e,t,n){var r=Ft(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(zd(e))Id(t,s);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var l=t.lastRenderedState,a=o(l,n);if(s.hasEagerState=!0,s.eagerState=a,nt(a,l)){var u=t.interleaved;u===null?(s.next=s,Di(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=hd(e,t,s,r),n!==null&&(s=_e(),et(n,e,r,s),Ad(n,t,r))}}function zd(e){var t=e.alternate;return e===ee||t!==null&&t===ee}function Id(e,t){pr=Ws=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ad(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ni(e,n)}}var Qs={readContext:Qe,useCallback:xe,useContext:xe,useEffect:xe,useImperativeHandle:xe,useInsertionEffect:xe,useLayoutEffect:xe,useMemo:xe,useReducer:xe,useRef:xe,useState:xe,useDebugValue:xe,useDeferredValue:xe,useTransition:xe,useMutableSource:xe,useSyncExternalStore:xe,useId:xe,unstable_isNewReconciler:!1},Zm={readContext:Qe,useCallback:function(e,t){return it().memoizedState=[e,t===void 0?null:t],e},useContext:Qe,useEffect:lu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,xs(4194308,4,Cd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xs(4194308,4,e,t)},useInsertionEffect:function(e,t){return xs(4,2,e,t)},useMemo:function(e,t){var n=it();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=it();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Xm.bind(null,ee,e),[r.memoizedState,e]},useRef:function(e){var t=it();return e={current:e},t.memoizedState=e},useState:ou,useDebugValue:qi,useDeferredValue:function(e){return it().memoizedState=e},useTransition:function(){var e=ou(!1),t=e[0];return e=Jm.bind(null,e[1]),it().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ee,s=it();if(X){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),me===null)throw Error(_(349));sn&30||wd(r,t,n)}s.memoizedState=n;var o={value:n,getSnapshot:t};return s.queue=o,lu(kd.bind(null,r,o,e),[e]),r.flags|=2048,Lr(9,Sd.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=it(),t=me.identifierPrefix;if(X){var n=ht,r=mt;n=(r&~(1<<32-Ze(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Km++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},eh={readContext:Qe,useCallback:Rd,useContext:Qe,useEffect:Qi,useImperativeHandle:Pd,useInsertionEffect:Ed,useLayoutEffect:bd,useMemo:Td,useReducer:Jo,useRef:_d,useState:function(){return Jo(Tr)},useDebugValue:qi,useDeferredValue:function(e){var t=qe();return Ld(t,de.memoizedState,e)},useTransition:function(){var e=Jo(Tr)[0],t=qe().memoizedState;return[e,t]},useMutableSource:vd,useSyncExternalStore:xd,useId:Od,unstable_isNewReconciler:!1},th={readContext:Qe,useCallback:Rd,useContext:Qe,useEffect:Qi,useImperativeHandle:Pd,useInsertionEffect:Ed,useLayoutEffect:bd,useMemo:Td,useReducer:Xo,useRef:_d,useState:function(){return Xo(Tr)},useDebugValue:qi,useDeferredValue:function(e){var t=qe();return de===null?t.memoizedState=e:Ld(t,de.memoizedState,e)},useTransition:function(){var e=Xo(Tr)[0],t=qe().memoizedState;return[e,t]},useMutableSource:vd,useSyncExternalStore:xd,useId:Od,unstable_isNewReconciler:!1};function Je(e,t){if(e&&e.defaultProps){t=te({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Bl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:te({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var uo={isMounted:function(e){return(e=e._reactInternals)?cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),s=Ft(e),o=gt(r,s);o.payload=t,n!=null&&(o.callback=n),t=Mt(e,o,s),t!==null&&(et(t,e,s,r),ys(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),s=Ft(e),o=gt(r,s);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Mt(e,o,s),t!==null&&(et(t,e,s,r),ys(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=Ft(e),s=gt(n,r);s.tag=2,t!=null&&(s.callback=t),t=Mt(e,s,r),t!==null&&(et(t,e,r,n),ys(t,e,r))}};function iu(e,t,n,r,s,o,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,l):t.prototype&&t.prototype.isPureReactComponent?!Nr(n,r)||!Nr(s,o):!0}function Md(e,t,n){var r=!1,s=$t,o=t.contextType;return typeof o=="object"&&o!==null?o=Qe(o):(s=Te(t)?nn:je.current,r=t.contextTypes,o=(r=r!=null)?On(e,s):$t),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=uo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=o),t}function au(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&uo.enqueueReplaceState(t,t.state,null)}function $l(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Fi(e);var o=t.contextType;typeof o=="object"&&o!==null?s.context=Qe(o):(o=Te(t)?nn:je.current,s.context=On(e,o)),s.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Bl(e,t,o,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&uo.enqueueReplaceState(s,s.state,null),Hs(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Mn(e,t){try{var n="",r=t;do n+=Pp(r),r=r.return;while(r);var s=n}catch(o){s=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:s,digest:null}}function Yo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var nh=typeof WeakMap=="function"?WeakMap:Map;function Dd(e,t,n){n=gt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Gs||(Gs=!0,Zl=r),Hl(e,t)},n}function Fd(e,t,n){n=gt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Hl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Hl(e,t),typeof r!="function"&&(Dt===null?Dt=new Set([this]):Dt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function uu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new nh;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=gh.bind(null,e,t,n),t.then(e,e))}function cu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function du(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=gt(-1,1),t.tag=2,Mt(n,t,1))),n.lanes|=1),e)}var rh=St.ReactCurrentOwner,Pe=!1;function Ne(e,t,n,r){t.child=e===null?md(t,null,n,r):In(t,e.child,n,r)}function fu(e,t,n,r,s){n=n.render;var o=t.ref;return Rn(t,s),r=Vi(e,t,n,r,o,s),n=Wi(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,wt(e,t,s)):(X&&n&&Li(t),t.flags|=1,Ne(e,t,r,s),t.child)}function pu(e,t,n,r,s){if(e===null){var o=n.type;return typeof o=="function"&&!ta(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Ud(e,t,o,r,s)):(e=js(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&s)){var l=o.memoizedProps;if(n=n.compare,n=n!==null?n:Nr,n(l,r)&&e.ref===t.ref)return wt(e,t,s)}return t.flags|=1,e=Ut(o,r),e.ref=t.ref,e.return=t,t.child=e}function Ud(e,t,n,r,s){if(e!==null){var o=e.memoizedProps;if(Nr(o,r)&&e.ref===t.ref)if(Pe=!1,t.pendingProps=r=o,(e.lanes&s)!==0)e.flags&131072&&(Pe=!0);else return t.lanes=e.lanes,wt(e,t,s)}return Vl(e,t,n,r,s)}function Bd(e,t,n){var r=t.pendingProps,s=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(_n,Ae),Ae|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(_n,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,W(_n,Ae),Ae|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,W(_n,Ae),Ae|=r;return Ne(e,t,s,n),t.child}function $d(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Vl(e,t,n,r,s){var o=Te(n)?nn:je.current;return o=On(t,o),Rn(t,s),n=Vi(e,t,n,r,o,s),r=Wi(),e!==null&&!Pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,wt(e,t,s)):(X&&r&&Li(t),t.flags|=1,Ne(e,t,n,s),t.child)}function mu(e,t,n,r,s){if(Te(n)){var o=!0;Ds(t)}else o=!1;if(Rn(t,s),t.stateNode===null)ws(e,t),Md(t,n,r),$l(t,n,r,s),r=!0;else if(e===null){var l=t.stateNode,a=t.memoizedProps;l.props=a;var u=l.context,c=n.contextType;typeof c=="object"&&c!==null?c=Qe(c):(c=Te(n)?nn:je.current,c=On(t,c));var f=n.getDerivedStateFromProps,m=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";m||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==r||u!==c)&&au(t,l,r,c),bt=!1;var g=t.memoizedState;l.state=g,Hs(t,r,l,s),u=t.memoizedState,a!==r||g!==u||Re.current||bt?(typeof f=="function"&&(Bl(t,n,f,r),u=t.memoizedState),(a=bt||iu(t,n,a,r,g,u,c))?(m||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=c,r=a):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,gd(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Je(t.type,a),l.props=c,m=t.pendingProps,g=l.context,u=n.contextType,typeof u=="object"&&u!==null?u=Qe(u):(u=Te(n)?nn:je.current,u=On(t,u));var x=n.getDerivedStateFromProps;(f=typeof x=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==m||g!==u)&&au(t,l,r,u),bt=!1,g=t.memoizedState,l.state=g,Hs(t,r,l,s);var y=t.memoizedState;a!==m||g!==y||Re.current||bt?(typeof x=="function"&&(Bl(t,n,x,r),y=t.memoizedState),(c=bt||iu(t,n,c,r,g,y,u)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,y,u),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,y,u)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),l.props=r,l.state=y,l.context=u,r=c):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),r=!1)}return Wl(e,t,n,r,o,s)}function Wl(e,t,n,r,s,o){$d(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return s&&Za(t,n,!1),wt(e,t,o);r=t.stateNode,rh.current=t;var a=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=In(t,e.child,null,o),t.child=In(t,null,a,o)):Ne(e,t,a,o),t.memoizedState=r.state,s&&Za(t,n,!0),t.child}function Hd(e){var t=e.stateNode;t.pendingContext?Ya(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ya(e,t.context,!1),Ui(e,t.containerInfo)}function hu(e,t,n,r,s){return zn(),zi(s),t.flags|=256,Ne(e,t,n,r),t.child}var Ql={dehydrated:null,treeContext:null,retryLane:0};function ql(e){return{baseLanes:e,cachePool:null,transitions:null}}function Vd(e,t,n){var r=t.pendingProps,s=Z.current,o=!1,l=(t.flags&128)!==0,a;if((a=l)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),W(Z,s&1),e===null)return Fl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,o?(r=t.mode,o=t.child,l={mode:"hidden",children:l},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=l):o=po(l,r,0,null),e=tn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ql(n),t.memoizedState=Ql,e):Gi(t,l));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return sh(e,t,l,r,a,s,n);if(o){o=r.fallback,l=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(l&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Ut(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?o=Ut(a,o):(o=tn(o,l,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,l=e.child.memoizedState,l=l===null?ql(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},o.memoizedState=l,o.childLanes=e.childLanes&~n,t.memoizedState=Ql,r}return o=e.child,e=o.sibling,r=Ut(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Gi(e,t){return t=po({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ls(e,t,n,r){return r!==null&&zi(r),In(t,e.child,null,n),e=Gi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sh(e,t,n,r,s,o,l){if(n)return t.flags&256?(t.flags&=-257,r=Yo(Error(_(422))),ls(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,s=t.mode,r=po({mode:"visible",children:r.children},s,0,null),o=tn(o,s,l,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&In(t,e.child,null,l),t.child.memoizedState=ql(l),t.memoizedState=Ql,o);if(!(t.mode&1))return ls(e,t,l,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(_(419)),r=Yo(o,r,void 0),ls(e,t,l,r)}if(a=(l&e.childLanes)!==0,Pe||a){if(r=me,r!==null){switch(l&-l){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|l)?0:s,s!==0&&s!==o.retryLane&&(o.retryLane=s,xt(e,s),et(r,e,s,-1))}return ea(),r=Yo(Error(_(421))),ls(e,t,l,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=yh.bind(null,e),s._reactRetry=t,null):(e=o.treeContext,Me=At(s.nextSibling),De=t,X=!0,Ye=null,e!==null&&($e[He++]=mt,$e[He++]=ht,$e[He++]=rn,mt=e.id,ht=e.overflow,rn=t),t=Gi(t,r.children),t.flags|=4096,t)}function gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ul(e.return,t,n)}function Zo(e,t,n,r,s){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=s)}function Wd(e,t,n){var r=t.pendingProps,s=r.revealOrder,o=r.tail;if(Ne(e,t,r.children,n),r=Z.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gu(e,n,t);else if(e.tag===19)gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(Z,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Vs(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Zo(t,!1,s,n,o);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Vs(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Zo(t,!0,n,null,o);break;case"together":Zo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ws(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function wt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),on|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Ut(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ut(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function oh(e,t,n){switch(t.tag){case 3:Hd(t),zn();break;case 5:yd(t);break;case 1:Te(t.type)&&Ds(t);break;case 4:Ui(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;W(Bs,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(Z,Z.current&1),t.flags|=128,null):n&t.child.childLanes?Vd(e,t,n):(W(Z,Z.current&1),e=wt(e,t,n),e!==null?e.sibling:null);W(Z,Z.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Wd(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),W(Z,Z.current),r)break;return null;case 22:case 23:return t.lanes=0,Bd(e,t,n)}return wt(e,t,n)}var Qd,Gl,qd,Gd;Qd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Gl=function(){};qd=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Xt(ct.current);var o=null;switch(n){case"input":s=gl(e,s),r=gl(e,r),o=[];break;case"select":s=te({},s,{value:void 0}),r=te({},r,{value:void 0}),o=[];break;case"textarea":s=xl(e,s),r=xl(e,r),o=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=As)}Sl(n,r);var l;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(l in a)a.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(yr.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(l in a)!a.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&a[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(yr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&Q("scroll",e),o||a===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Gd=function(e,t,n,r){n!==r&&(t.flags|=4)};function er(e,t){if(!X)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function lh(e,t,n){var r=t.pendingProps;switch(Oi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return Te(t.type)&&Ms(),we(t),null;case 3:return r=t.stateNode,An(),G(Re),G(je),$i(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ss(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(ni(Ye),Ye=null))),Gl(e,t),we(t),null;case 5:Bi(t);var s=Xt(Pr.current);if(n=t.type,e!==null&&t.stateNode!=null)qd(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return we(t),null}if(e=Xt(ct.current),ss(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[at]=t,r[br]=o,e=(t.mode&1)!==0,n){case"dialog":Q("cancel",r),Q("close",r);break;case"iframe":case"object":case"embed":Q("load",r);break;case"video":case"audio":for(s=0;s<ir.length;s++)Q(ir[s],r);break;case"source":Q("error",r);break;case"img":case"image":case"link":Q("error",r),Q("load",r);break;case"details":Q("toggle",r);break;case"input":_a(r,o),Q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Q("invalid",r);break;case"textarea":ba(r,o),Q("invalid",r)}Sl(n,o),s=null;for(var l in o)if(o.hasOwnProperty(l)){var a=o[l];l==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&rs(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&rs(r.textContent,a,e),s=["children",""+a]):yr.hasOwnProperty(l)&&a!=null&&l==="onScroll"&&Q("scroll",r)}switch(n){case"input":Kr(r),Ea(r,o,!0);break;case"textarea":Kr(r),Ca(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=As)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=kc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[at]=t,e[br]=r,Qd(e,t,!1,!1),t.stateNode=e;e:{switch(l=kl(n,r),n){case"dialog":Q("cancel",e),Q("close",e),s=r;break;case"iframe":case"object":case"embed":Q("load",e),s=r;break;case"video":case"audio":for(s=0;s<ir.length;s++)Q(ir[s],e);s=r;break;case"source":Q("error",e),s=r;break;case"img":case"image":case"link":Q("error",e),Q("load",e),s=r;break;case"details":Q("toggle",e),s=r;break;case"input":_a(e,r),s=gl(e,r),Q("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=te({},r,{value:void 0}),Q("invalid",e);break;case"textarea":ba(e,r),s=xl(e,r),Q("invalid",e);break;default:s=r}Sl(n,s),a=s;for(o in a)if(a.hasOwnProperty(o)){var u=a[o];o==="style"?_c(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&jc(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&vr(e,u):typeof u=="number"&&vr(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(yr.hasOwnProperty(o)?u!=null&&o==="onScroll"&&Q("scroll",e):u!=null&&vi(e,o,u,l))}switch(n){case"input":Kr(e),Ea(e,r,!1);break;case"textarea":Kr(e),Ca(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Bt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?En(e,!!r.multiple,o,!1):r.defaultValue!=null&&En(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=As)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return we(t),null;case 6:if(e&&t.stateNode!=null)Gd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=Xt(Pr.current),Xt(ct.current),ss(t)){if(r=t.stateNode,n=t.memoizedProps,r[at]=t,(o=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:rs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&rs(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[at]=t,t.stateNode=r}return we(t),null;case 13:if(G(Z),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(X&&Me!==null&&t.mode&1&&!(t.flags&128))fd(),zn(),t.flags|=98560,o=!1;else if(o=ss(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[at]=t}else zn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;we(t),o=!1}else Ye!==null&&(ni(Ye),Ye=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Z.current&1?fe===0&&(fe=3):ea())),t.updateQueue!==null&&(t.flags|=4),we(t),null);case 4:return An(),Gl(e,t),e===null&&_r(t.stateNode.containerInfo),we(t),null;case 10:return Mi(t.type._context),we(t),null;case 17:return Te(t.type)&&Ms(),we(t),null;case 19:if(G(Z),o=t.memoizedState,o===null)return we(t),null;if(r=(t.flags&128)!==0,l=o.rendering,l===null)if(r)er(o,!1);else{if(fe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Vs(e),l!==null){for(t.flags|=128,er(o,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,l=o.alternate,l===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(Z,Z.current&1|2),t.child}e=e.sibling}o.tail!==null&&oe()>Dn&&(t.flags|=128,r=!0,er(o,!1),t.lanes=4194304)}else{if(!r)if(e=Vs(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),er(o,!0),o.tail===null&&o.tailMode==="hidden"&&!l.alternate&&!X)return we(t),null}else 2*oe()-o.renderingStartTime>Dn&&n!==1073741824&&(t.flags|=128,r=!0,er(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(n=o.last,n!==null?n.sibling=l:t.child=l,o.last=l)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=oe(),t.sibling=null,n=Z.current,W(Z,r?n&1|2:n&1),t):(we(t),null);case 22:case 23:return Zi(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function ih(e,t){switch(Oi(t),t.tag){case 1:return Te(t.type)&&Ms(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return An(),G(Re),G(je),$i(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Bi(t),null;case 13:if(G(Z),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));zn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(Z),null;case 4:return An(),null;case 10:return Mi(t.type._context),null;case 22:case 23:return Zi(),null;case 24:return null;default:return null}}var is=!1,Se=!1,ah=typeof WeakSet=="function"?WeakSet:Set,R=null;function Nn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){re(e,t,r)}else n.current=null}function Kl(e,t,n){try{n()}catch(r){re(e,t,r)}}var yu=!1;function uh(e,t){if(Ll=Os,e=Zc(),Ti(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var l=0,a=-1,u=-1,c=0,f=0,m=e,g=null;t:for(;;){for(var x;m!==n||s!==0&&m.nodeType!==3||(a=l+s),m!==o||r!==0&&m.nodeType!==3||(u=l+r),m.nodeType===3&&(l+=m.nodeValue.length),(x=m.firstChild)!==null;)g=m,m=x;for(;;){if(m===e)break t;if(g===n&&++c===s&&(a=l),g===o&&++f===r&&(u=l),(x=m.nextSibling)!==null)break;m=g,g=m.parentNode}m=x}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ol={focusedElem:e,selectionRange:n},Os=!1,R=t;R!==null;)if(t=R,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,R=e;else for(;R!==null;){t=R;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,w=y.memoizedState,p=t.stateNode,d=p.getSnapshotBeforeUpdate(t.elementType===t.type?v:Je(t.type,v),w);p.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(k){re(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,R=e;break}R=t.return}return y=yu,yu=!1,y}function mr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var o=s.destroy;s.destroy=void 0,o!==void 0&&Kl(t,n,o)}s=s.next}while(s!==r)}}function co(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Jl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Kd(e){var t=e.alternate;t!==null&&(e.alternate=null,Kd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[at],delete t[br],delete t[Al],delete t[Wm],delete t[Qm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Jd(e){return e.tag===5||e.tag===3||e.tag===4}function vu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Jd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=As));else if(r!==4&&(e=e.child,e!==null))for(Xl(e,t,n),e=e.sibling;e!==null;)Xl(e,t,n),e=e.sibling}function Yl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Yl(e,t,n),e=e.sibling;e!==null;)Yl(e,t,n),e=e.sibling}var he=null,Xe=!1;function Nt(e,t,n){for(n=n.child;n!==null;)Xd(e,t,n),n=n.sibling}function Xd(e,t,n){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(no,n)}catch{}switch(n.tag){case 5:Se||Nn(n,t);case 6:var r=he,s=Xe;he=null,Nt(e,t,n),he=r,Xe=s,he!==null&&(Xe?(e=he,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):he.removeChild(n.stateNode));break;case 18:he!==null&&(Xe?(e=he,n=n.stateNode,e.nodeType===8?Qo(e.parentNode,n):e.nodeType===1&&Qo(e,n),kr(e)):Qo(he,n.stateNode));break;case 4:r=he,s=Xe,he=n.stateNode.containerInfo,Xe=!0,Nt(e,t,n),he=r,Xe=s;break;case 0:case 11:case 14:case 15:if(!Se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var o=s,l=o.destroy;o=o.tag,l!==void 0&&(o&2||o&4)&&Kl(n,t,l),s=s.next}while(s!==r)}Nt(e,t,n);break;case 1:if(!Se&&(Nn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){re(n,t,a)}Nt(e,t,n);break;case 21:Nt(e,t,n);break;case 22:n.mode&1?(Se=(r=Se)||n.memoizedState!==null,Nt(e,t,n),Se=r):Nt(e,t,n);break;default:Nt(e,t,n)}}function xu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new ah),t.forEach(function(r){var s=vh.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ke(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var o=e,l=t,a=l;e:for(;a!==null;){switch(a.tag){case 5:he=a.stateNode,Xe=!1;break e;case 3:he=a.stateNode.containerInfo,Xe=!0;break e;case 4:he=a.stateNode.containerInfo,Xe=!0;break e}a=a.return}if(he===null)throw Error(_(160));Xd(o,l,s),he=null,Xe=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){re(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Yd(t,e),t=t.sibling}function Yd(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ke(t,e),ot(e),r&4){try{mr(3,e,e.return),co(3,e)}catch(v){re(e,e.return,v)}try{mr(5,e,e.return)}catch(v){re(e,e.return,v)}}break;case 1:Ke(t,e),ot(e),r&512&&n!==null&&Nn(n,n.return);break;case 5:if(Ke(t,e),ot(e),r&512&&n!==null&&Nn(n,n.return),e.flags&32){var s=e.stateNode;try{vr(s,"")}catch(v){re(e,e.return,v)}}if(r&4&&(s=e.stateNode,s!=null)){var o=e.memoizedProps,l=n!==null?n.memoizedProps:o,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&wc(s,o),kl(a,l);var c=kl(a,o);for(l=0;l<u.length;l+=2){var f=u[l],m=u[l+1];f==="style"?_c(s,m):f==="dangerouslySetInnerHTML"?jc(s,m):f==="children"?vr(s,m):vi(s,f,m,c)}switch(a){case"input":yl(s,o);break;case"textarea":Sc(s,o);break;case"select":var g=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!o.multiple;var x=o.value;x!=null?En(s,!!o.multiple,x,!1):g!==!!o.multiple&&(o.defaultValue!=null?En(s,!!o.multiple,o.defaultValue,!0):En(s,!!o.multiple,o.multiple?[]:"",!1))}s[br]=o}catch(v){re(e,e.return,v)}}break;case 6:if(Ke(t,e),ot(e),r&4){if(e.stateNode===null)throw Error(_(162));s=e.stateNode,o=e.memoizedProps;try{s.nodeValue=o}catch(v){re(e,e.return,v)}}break;case 3:if(Ke(t,e),ot(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{kr(t.containerInfo)}catch(v){re(e,e.return,v)}break;case 4:Ke(t,e),ot(e);break;case 13:Ke(t,e),ot(e),s=e.child,s.flags&8192&&(o=s.memoizedState!==null,s.stateNode.isHidden=o,!o||s.alternate!==null&&s.alternate.memoizedState!==null||(Xi=oe())),r&4&&xu(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(Se=(c=Se)||f,Ke(t,e),Se=c):Ke(t,e),ot(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(R=e,f=e.child;f!==null;){for(m=R=f;R!==null;){switch(g=R,x=g.child,g.tag){case 0:case 11:case 14:case 15:mr(4,g,g.return);break;case 1:Nn(g,g.return);var y=g.stateNode;if(typeof y.componentWillUnmount=="function"){r=g,n=g.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){re(r,n,v)}}break;case 5:Nn(g,g.return);break;case 22:if(g.memoizedState!==null){Su(m);continue}}x!==null?(x.return=g,R=x):Su(m)}f=f.sibling}e:for(f=null,m=e;;){if(m.tag===5){if(f===null){f=m;try{s=m.stateNode,c?(o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=m.stateNode,u=m.memoizedProps.style,l=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Nc("display",l))}catch(v){re(e,e.return,v)}}}else if(m.tag===6){if(f===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(v){re(e,e.return,v)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;f===m&&(f=null),m=m.return}f===m&&(f=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Ke(t,e),ot(e),r&4&&xu(e);break;case 21:break;default:Ke(t,e),ot(e)}}function ot(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Jd(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(vr(s,""),r.flags&=-33);var o=vu(e);Yl(e,o,s);break;case 3:case 4:var l=r.stateNode.containerInfo,a=vu(e);Xl(e,a,l);break;default:throw Error(_(161))}}catch(u){re(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ch(e,t,n){R=e,Zd(e)}function Zd(e,t,n){for(var r=(e.mode&1)!==0;R!==null;){var s=R,o=s.child;if(s.tag===22&&r){var l=s.memoizedState!==null||is;if(!l){var a=s.alternate,u=a!==null&&a.memoizedState!==null||Se;a=is;var c=Se;if(is=l,(Se=u)&&!c)for(R=s;R!==null;)l=R,u=l.child,l.tag===22&&l.memoizedState!==null?ku(s):u!==null?(u.return=l,R=u):ku(s);for(;o!==null;)R=o,Zd(o),o=o.sibling;R=s,is=a,Se=c}wu(e)}else s.subtreeFlags&8772&&o!==null?(o.return=s,R=o):wu(e)}}function wu(e){for(;R!==null;){var t=R;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Se||co(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Se)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Je(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&su(t,o,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}su(t,l,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var m=f.dehydrated;m!==null&&kr(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Se||t.flags&512&&Jl(t)}catch(g){re(t,t.return,g)}}if(t===e){R=null;break}if(n=t.sibling,n!==null){n.return=t.return,R=n;break}R=t.return}}function Su(e){for(;R!==null;){var t=R;if(t===e){R=null;break}var n=t.sibling;if(n!==null){n.return=t.return,R=n;break}R=t.return}}function ku(e){for(;R!==null;){var t=R;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{co(4,t)}catch(u){re(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){re(t,s,u)}}var o=t.return;try{Jl(t)}catch(u){re(t,o,u)}break;case 5:var l=t.return;try{Jl(t)}catch(u){re(t,l,u)}}}catch(u){re(t,t.return,u)}if(t===e){R=null;break}var a=t.sibling;if(a!==null){a.return=t.return,R=a;break}R=t.return}}var dh=Math.ceil,qs=St.ReactCurrentDispatcher,Ki=St.ReactCurrentOwner,We=St.ReactCurrentBatchConfig,B=0,me=null,ie=null,ge=0,Ae=0,_n=Vt(0),fe=0,Or=null,on=0,fo=0,Ji=0,hr=null,Ce=null,Xi=0,Dn=1/0,ft=null,Gs=!1,Zl=null,Dt=null,as=!1,Tt=null,Ks=0,gr=0,ei=null,Ss=-1,ks=0;function _e(){return B&6?oe():Ss!==-1?Ss:Ss=oe()}function Ft(e){return e.mode&1?B&2&&ge!==0?ge&-ge:Gm.transition!==null?(ks===0&&(ks=Mc()),ks):(e=V,e!==0||(e=window.event,e=e===void 0?16:Vc(e.type)),e):1}function et(e,t,n,r){if(50<gr)throw gr=0,ei=null,Error(_(185));Mr(e,n,r),(!(B&2)||e!==me)&&(e===me&&(!(B&2)&&(fo|=n),fe===4&&Pt(e,ge)),Le(e,r),n===1&&B===0&&!(t.mode&1)&&(Dn=oe()+500,io&&Wt()))}function Le(e,t){var n=e.callbackNode;Gp(e,t);var r=Ls(e,e===me?ge:0);if(r===0)n!==null&&Ta(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ta(n),t===1)e.tag===0?qm(ju.bind(null,e)):ud(ju.bind(null,e)),Hm(function(){!(B&6)&&Wt()}),n=null;else{switch(Dc(r)){case 1:n=ji;break;case 4:n=Ic;break;case 16:n=Ts;break;case 536870912:n=Ac;break;default:n=Ts}n=af(n,ef.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ef(e,t){if(Ss=-1,ks=0,B&6)throw Error(_(327));var n=e.callbackNode;if(Tn()&&e.callbackNode!==n)return null;var r=Ls(e,e===me?ge:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Js(e,r);else{t=r;var s=B;B|=2;var o=nf();(me!==e||ge!==t)&&(ft=null,Dn=oe()+500,en(e,t));do try{mh();break}catch(a){tf(e,a)}while(1);Ai(),qs.current=o,B=s,ie!==null?t=0:(me=null,ge=0,t=fe)}if(t!==0){if(t===2&&(s=bl(e),s!==0&&(r=s,t=ti(e,s))),t===1)throw n=Or,en(e,0),Pt(e,r),Le(e,oe()),n;if(t===6)Pt(e,r);else{if(s=e.current.alternate,!(r&30)&&!fh(s)&&(t=Js(e,r),t===2&&(o=bl(e),o!==0&&(r=o,t=ti(e,o))),t===1))throw n=Or,en(e,0),Pt(e,r),Le(e,oe()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:Gt(e,Ce,ft);break;case 3:if(Pt(e,r),(r&130023424)===r&&(t=Xi+500-oe(),10<t)){if(Ls(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Il(Gt.bind(null,e,Ce,ft),t);break}Gt(e,Ce,ft);break;case 4:if(Pt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var l=31-Ze(r);o=1<<l,l=t[l],l>s&&(s=l),r&=~o}if(r=s,r=oe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*dh(r/1960))-r,10<r){e.timeoutHandle=Il(Gt.bind(null,e,Ce,ft),r);break}Gt(e,Ce,ft);break;case 5:Gt(e,Ce,ft);break;default:throw Error(_(329))}}}return Le(e,oe()),e.callbackNode===n?ef.bind(null,e):null}function ti(e,t){var n=hr;return e.current.memoizedState.isDehydrated&&(en(e,t).flags|=256),e=Js(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&ni(t)),e}function ni(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function fh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],o=s.getSnapshot;s=s.value;try{if(!nt(o(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Pt(e,t){for(t&=~Ji,t&=~fo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ze(t),r=1<<n;e[n]=-1,t&=~r}}function ju(e){if(B&6)throw Error(_(327));Tn();var t=Ls(e,0);if(!(t&1))return Le(e,oe()),null;var n=Js(e,t);if(e.tag!==0&&n===2){var r=bl(e);r!==0&&(t=r,n=ti(e,r))}if(n===1)throw n=Or,en(e,0),Pt(e,t),Le(e,oe()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Gt(e,Ce,ft),Le(e,oe()),null}function Yi(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(Dn=oe()+500,io&&Wt())}}function ln(e){Tt!==null&&Tt.tag===0&&!(B&6)&&Tn();var t=B;B|=1;var n=We.transition,r=V;try{if(We.transition=null,V=1,e)return e()}finally{V=r,We.transition=n,B=t,!(B&6)&&Wt()}}function Zi(){Ae=_n.current,G(_n)}function en(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,$m(n)),ie!==null)for(n=ie.return;n!==null;){var r=n;switch(Oi(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ms();break;case 3:An(),G(Re),G(je),$i();break;case 5:Bi(r);break;case 4:An();break;case 13:G(Z);break;case 19:G(Z);break;case 10:Mi(r.type._context);break;case 22:case 23:Zi()}n=n.return}if(me=e,ie=e=Ut(e.current,null),ge=Ae=t,fe=0,Or=null,Ji=fo=on=0,Ce=hr=null,Jt!==null){for(t=0;t<Jt.length;t++)if(n=Jt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,o=n.pending;if(o!==null){var l=o.next;o.next=s,r.next=l}n.pending=r}Jt=null}return e}function tf(e,t){do{var n=ie;try{if(Ai(),vs.current=Qs,Ws){for(var r=ee.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ws=!1}if(sn=0,pe=de=ee=null,pr=!1,Rr=0,Ki.current=null,n===null||n.return===null){fe=1,Or=t,ie=null;break}e:{var o=e,l=n.return,a=n,u=t;if(t=ge,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=a,m=f.tag;if(!(f.mode&1)&&(m===0||m===11||m===15)){var g=f.alternate;g?(f.updateQueue=g.updateQueue,f.memoizedState=g.memoizedState,f.lanes=g.lanes):(f.updateQueue=null,f.memoizedState=null)}var x=cu(l);if(x!==null){x.flags&=-257,du(x,l,a,o,t),x.mode&1&&uu(o,c,t),t=x,u=c;var y=t.updateQueue;if(y===null){var v=new Set;v.add(u),t.updateQueue=v}else y.add(u);break e}else{if(!(t&1)){uu(o,c,t),ea();break e}u=Error(_(426))}}else if(X&&a.mode&1){var w=cu(l);if(w!==null){!(w.flags&65536)&&(w.flags|=256),du(w,l,a,o,t),zi(Mn(u,a));break e}}o=u=Mn(u,a),fe!==4&&(fe=2),hr===null?hr=[o]:hr.push(o),o=l;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=Dd(o,u,t);ru(o,p);break e;case 1:a=u;var d=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof d.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Dt===null||!Dt.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var k=Fd(o,a,t);ru(o,k);break e}}o=o.return}while(o!==null)}sf(n)}catch(E){t=E,ie===n&&n!==null&&(ie=n=n.return);continue}break}while(1)}function nf(){var e=qs.current;return qs.current=Qs,e===null?Qs:e}function ea(){(fe===0||fe===3||fe===2)&&(fe=4),me===null||!(on&268435455)&&!(fo&268435455)||Pt(me,ge)}function Js(e,t){var n=B;B|=2;var r=nf();(me!==e||ge!==t)&&(ft=null,en(e,t));do try{ph();break}catch(s){tf(e,s)}while(1);if(Ai(),B=n,qs.current=r,ie!==null)throw Error(_(261));return me=null,ge=0,fe}function ph(){for(;ie!==null;)rf(ie)}function mh(){for(;ie!==null&&!Fp();)rf(ie)}function rf(e){var t=lf(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?sf(e):ie=t,Ki.current=null}function sf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ih(n,t),n!==null){n.flags&=32767,ie=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{fe=6,ie=null;return}}else if(n=lh(n,t,Ae),n!==null){ie=n;return}if(t=t.sibling,t!==null){ie=t;return}ie=t=e}while(t!==null);fe===0&&(fe=5)}function Gt(e,t,n){var r=V,s=We.transition;try{We.transition=null,V=1,hh(e,t,n,r)}finally{We.transition=s,V=r}return null}function hh(e,t,n,r){do Tn();while(Tt!==null);if(B&6)throw Error(_(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Kp(e,o),e===me&&(ie=me=null,ge=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||as||(as=!0,af(Ts,function(){return Tn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=We.transition,We.transition=null;var l=V;V=1;var a=B;B|=4,Ki.current=null,uh(e,n),Yd(n,e),Im(Ol),Os=!!Ll,Ol=Ll=null,e.current=n,ch(n),Up(),B=a,V=l,We.transition=o}else e.current=n;if(as&&(as=!1,Tt=e,Ks=s),o=e.pendingLanes,o===0&&(Dt=null),Hp(n.stateNode),Le(e,oe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Gs)throw Gs=!1,e=Zl,Zl=null,e;return Ks&1&&e.tag!==0&&Tn(),o=e.pendingLanes,o&1?e===ei?gr++:(gr=0,ei=e):gr=0,Wt(),null}function Tn(){if(Tt!==null){var e=Dc(Ks),t=We.transition,n=V;try{if(We.transition=null,V=16>e?16:e,Tt===null)var r=!1;else{if(e=Tt,Tt=null,Ks=0,B&6)throw Error(_(331));var s=B;for(B|=4,R=e.current;R!==null;){var o=R,l=o.child;if(R.flags&16){var a=o.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(R=c;R!==null;){var f=R;switch(f.tag){case 0:case 11:case 15:mr(8,f,o)}var m=f.child;if(m!==null)m.return=f,R=m;else for(;R!==null;){f=R;var g=f.sibling,x=f.return;if(Kd(f),f===c){R=null;break}if(g!==null){g.return=x,R=g;break}R=x}}}var y=o.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}R=o}}if(o.subtreeFlags&2064&&l!==null)l.return=o,R=l;else e:for(;R!==null;){if(o=R,o.flags&2048)switch(o.tag){case 0:case 11:case 15:mr(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,R=p;break e}R=o.return}}var d=e.current;for(R=d;R!==null;){l=R;var h=l.child;if(l.subtreeFlags&2064&&h!==null)h.return=l,R=h;else e:for(l=d;R!==null;){if(a=R,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:co(9,a)}}catch(E){re(a,a.return,E)}if(a===l){R=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,R=k;break e}R=a.return}}if(B=s,Wt(),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(no,e)}catch{}r=!0}return r}finally{V=n,We.transition=t}}return!1}function Nu(e,t,n){t=Mn(n,t),t=Dd(e,t,1),e=Mt(e,t,1),t=_e(),e!==null&&(Mr(e,1,t),Le(e,t))}function re(e,t,n){if(e.tag===3)Nu(e,e,n);else for(;t!==null;){if(t.tag===3){Nu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Dt===null||!Dt.has(r))){e=Mn(n,e),e=Fd(t,e,1),t=Mt(t,e,1),e=_e(),t!==null&&(Mr(t,1,e),Le(t,e));break}}t=t.return}}function gh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,me===e&&(ge&n)===n&&(fe===4||fe===3&&(ge&130023424)===ge&&500>oe()-Xi?en(e,0):Ji|=n),Le(e,t)}function of(e,t){t===0&&(e.mode&1?(t=Yr,Yr<<=1,!(Yr&130023424)&&(Yr=4194304)):t=1);var n=_e();e=xt(e,t),e!==null&&(Mr(e,t,n),Le(e,n))}function yh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),of(e,n)}function vh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),of(e,n)}var lf;lf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Re.current)Pe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Pe=!1,oh(e,t,n);Pe=!!(e.flags&131072)}else Pe=!1,X&&t.flags&1048576&&cd(t,Us,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ws(e,t),e=t.pendingProps;var s=On(t,je.current);Rn(t,n),s=Vi(null,t,r,e,s,n);var o=Wi();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(o=!0,Ds(t)):o=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Fi(t),s.updater=uo,t.stateNode=s,s._reactInternals=t,$l(t,r,e,n),t=Wl(null,t,r,!0,o,n)):(t.tag=0,X&&o&&Li(t),Ne(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ws(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=wh(r),e=Je(r,e),s){case 0:t=Vl(null,t,r,e,n);break e;case 1:t=mu(null,t,r,e,n);break e;case 11:t=fu(null,t,r,e,n);break e;case 14:t=pu(null,t,r,Je(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Je(r,s),Vl(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Je(r,s),mu(e,t,r,s,n);case 3:e:{if(Hd(t),e===null)throw Error(_(387));r=t.pendingProps,o=t.memoizedState,s=o.element,gd(e,t),Hs(t,r,null,n);var l=t.memoizedState;if(r=l.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){s=Mn(Error(_(423)),t),t=hu(e,t,r,n,s);break e}else if(r!==s){s=Mn(Error(_(424)),t),t=hu(e,t,r,n,s);break e}else for(Me=At(t.stateNode.containerInfo.firstChild),De=t,X=!0,Ye=null,n=md(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(zn(),r===s){t=wt(e,t,n);break e}Ne(e,t,r,n)}t=t.child}return t;case 5:return yd(t),e===null&&Fl(t),r=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,l=s.children,zl(r,s)?l=null:o!==null&&zl(r,o)&&(t.flags|=32),$d(e,t),Ne(e,t,l,n),t.child;case 6:return e===null&&Fl(t),null;case 13:return Vd(e,t,n);case 4:return Ui(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=In(t,null,r,n):Ne(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Je(r,s),fu(e,t,r,s,n);case 7:return Ne(e,t,t.pendingProps,n),t.child;case 8:return Ne(e,t,t.pendingProps.children,n),t.child;case 12:return Ne(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,o=t.memoizedProps,l=s.value,W(Bs,r._currentValue),r._currentValue=l,o!==null)if(nt(o.value,l)){if(o.children===s.children&&!Re.current){t=wt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){l=o.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=gt(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Ul(o.return,n,t),a.lanes|=n;break}u=u.next}}else if(o.tag===10)l=o.type===t.type?null:o.child;else if(o.tag===18){if(l=o.return,l===null)throw Error(_(341));l.lanes|=n,a=l.alternate,a!==null&&(a.lanes|=n),Ul(l,n,t),l=o.sibling}else l=o.child;if(l!==null)l.return=o;else for(l=o;l!==null;){if(l===t){l=null;break}if(o=l.sibling,o!==null){o.return=l.return,l=o;break}l=l.return}o=l}Ne(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Rn(t,n),s=Qe(s),r=r(s),t.flags|=1,Ne(e,t,r,n),t.child;case 14:return r=t.type,s=Je(r,t.pendingProps),s=Je(r.type,s),pu(e,t,r,s,n);case 15:return Ud(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Je(r,s),ws(e,t),t.tag=1,Te(r)?(e=!0,Ds(t)):e=!1,Rn(t,n),Md(t,r,s),$l(t,r,s,n),Wl(null,t,r,!0,e,n);case 19:return Wd(e,t,n);case 22:return Bd(e,t,n)}throw Error(_(156,t.tag))};function af(e,t){return zc(e,t)}function xh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ve(e,t,n,r){return new xh(e,t,n,r)}function ta(e){return e=e.prototype,!(!e||!e.isReactComponent)}function wh(e){if(typeof e=="function")return ta(e)?1:0;if(e!=null){if(e=e.$$typeof,e===wi)return 11;if(e===Si)return 14}return 2}function Ut(e,t){var n=e.alternate;return n===null?(n=Ve(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function js(e,t,n,r,s,o){var l=2;if(r=e,typeof e=="function")ta(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case hn:return tn(n.children,s,o,t);case xi:l=8,s|=8;break;case fl:return e=Ve(12,n,t,s|2),e.elementType=fl,e.lanes=o,e;case pl:return e=Ve(13,n,t,s),e.elementType=pl,e.lanes=o,e;case ml:return e=Ve(19,n,t,s),e.elementType=ml,e.lanes=o,e;case yc:return po(n,s,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case hc:l=10;break e;case gc:l=9;break e;case wi:l=11;break e;case Si:l=14;break e;case Et:l=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=Ve(l,n,t,s),t.elementType=e,t.type=r,t.lanes=o,t}function tn(e,t,n,r){return e=Ve(7,e,r,t),e.lanes=n,e}function po(e,t,n,r){return e=Ve(22,e,r,t),e.elementType=yc,e.lanes=n,e.stateNode={isHidden:!1},e}function el(e,t,n){return e=Ve(6,e,null,t),e.lanes=n,e}function tl(e,t,n){return t=Ve(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Sh(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Io(0),this.expirationTimes=Io(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Io(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function na(e,t,n,r,s,o,l,a,u){return e=new Sh(e,t,n,a,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ve(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fi(o),e}function kh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function uf(e){if(!e)return $t;e=e._reactInternals;e:{if(cn(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(Te(n))return ad(e,n,t)}return t}function cf(e,t,n,r,s,o,l,a,u){return e=na(n,r,!0,e,s,o,l,a,u),e.context=uf(null),n=e.current,r=_e(),s=Ft(n),o=gt(r,s),o.callback=t??null,Mt(n,o,s),e.current.lanes=s,Mr(e,s,r),Le(e,r),e}function mo(e,t,n,r){var s=t.current,o=_e(),l=Ft(s);return n=uf(n),t.context===null?t.context=n:t.pendingContext=n,t=gt(o,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Mt(s,t,l),e!==null&&(et(e,s,l,o),ys(e,s,l)),l}function Xs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function _u(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ra(e,t){_u(e,t),(e=e.alternate)&&_u(e,t)}function jh(){return null}var df=typeof reportError=="function"?reportError:function(e){console.error(e)};function sa(e){this._internalRoot=e}ho.prototype.render=sa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));mo(e,t,null,null)};ho.prototype.unmount=sa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ln(function(){mo(null,e,null,null)}),t[vt]=null}};function ho(e){this._internalRoot=e}ho.prototype.unstable_scheduleHydration=function(e){if(e){var t=Bc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&Hc(e)}};function oa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function go(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Eu(){}function Nh(e,t,n,r,s){if(s){if(typeof r=="function"){var o=r;r=function(){var c=Xs(l);o.call(c)}}var l=cf(t,r,e,0,null,!1,!1,"",Eu);return e._reactRootContainer=l,e[vt]=l.current,_r(e.nodeType===8?e.parentNode:e),ln(),l}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=Xs(u);a.call(c)}}var u=na(e,0,!1,null,null,!1,!1,"",Eu);return e._reactRootContainer=u,e[vt]=u.current,_r(e.nodeType===8?e.parentNode:e),ln(function(){mo(t,u,n,r)}),u}function yo(e,t,n,r,s){var o=n._reactRootContainer;if(o){var l=o;if(typeof s=="function"){var a=s;s=function(){var u=Xs(l);a.call(u)}}mo(t,l,e,s)}else l=Nh(n,t,e,s,r);return Xs(l)}Fc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=lr(t.pendingLanes);n!==0&&(Ni(t,n|1),Le(t,oe()),!(B&6)&&(Dn=oe()+500,Wt()))}break;case 13:ln(function(){var r=xt(e,1);if(r!==null){var s=_e();et(r,e,1,s)}}),ra(e,1)}};_i=function(e){if(e.tag===13){var t=xt(e,134217728);if(t!==null){var n=_e();et(t,e,134217728,n)}ra(e,134217728)}};Uc=function(e){if(e.tag===13){var t=Ft(e),n=xt(e,t);if(n!==null){var r=_e();et(n,e,t,r)}ra(e,t)}};Bc=function(){return V};$c=function(e,t){var n=V;try{return V=e,t()}finally{V=n}};Nl=function(e,t,n){switch(t){case"input":if(yl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=lo(r);if(!s)throw Error(_(90));xc(r),yl(r,s)}}}break;case"textarea":Sc(e,n);break;case"select":t=n.value,t!=null&&En(e,!!n.multiple,t,!1)}};Cc=Yi;Pc=ln;var _h={usingClientEntryPoint:!1,Events:[Fr,xn,lo,Ec,bc,Yi]},tr={findFiberByHostInstance:Kt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Eh={bundleType:tr.bundleType,version:tr.version,rendererPackageName:tr.rendererPackageName,rendererConfig:tr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:St.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Lc(e),e===null?null:e.stateNode},findFiberByHostInstance:tr.findFiberByHostInstance||jh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var us=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!us.isDisabled&&us.supportsFiber)try{no=us.inject(Eh),ut=us}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_h;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!oa(t))throw Error(_(200));return kh(e,t,null,n)};Ue.createRoot=function(e,t){if(!oa(e))throw Error(_(299));var n=!1,r="",s=df;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=na(e,1,!1,null,null,n,!1,r,s),e[vt]=t.current,_r(e.nodeType===8?e.parentNode:e),new sa(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Lc(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return ln(e)};Ue.hydrate=function(e,t,n){if(!go(t))throw Error(_(200));return yo(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!oa(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,s=!1,o="",l=df;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=cf(t,null,e,1,n??null,s,!1,o,l),e[vt]=t.current,_r(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new ho(t)};Ue.render=function(e,t,n){if(!go(t))throw Error(_(200));return yo(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!go(e))throw Error(_(40));return e._reactRootContainer?(ln(function(){yo(null,null,e,!1,function(){e._reactRootContainer=null,e[vt]=null})}),!0):!1};Ue.unstable_batchedUpdates=Yi;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!go(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return yo(e,t,n,!1,r)};Ue.version="18.3.1-next-f1338f8080-20240426";function ff(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ff)}catch(e){console.error(e)}}ff(),dc.exports=Ue;var pf=dc.exports,bu=pf;cl.createRoot=bu.createRoot,cl.hydrateRoot=bu.hydrateRoot;const bh=({activeTab:e,onTabChange:t,tabs:n})=>i.jsx("div",{className:"notion-wp-sidebar",children:i.jsx("div",{className:"notion-wp-menu",children:n.map(r=>i.jsx("button",{className:`notion-wp-menu-item ${e===r.id?"active":""}`,onClick:()=>t(r.id),children:r.label},r.id))})}),Cu=e=>{let t;const n=new Set,r=(f,m)=>{const g=typeof f=="function"?f(t):f;if(!Object.is(g,t)){const x=t;t=m??(typeof g!="object"||g===null)?g:Object.assign({},t,g),n.forEach(y=>y(t,x))}},s=()=>t,u={setState:r,getState:s,getInitialState:()=>c,subscribe:f=>(n.add(f),()=>n.delete(f)),destroy:()=>{n.clear()}},c=t=e(r,s,u);return u},Ch=e=>e?Cu(e):Cu;var mf={exports:{}},hf={},gf={exports:{}},yf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fn=M;function Ph(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Rh=typeof Object.is=="function"?Object.is:Ph,Th=Fn.useState,Lh=Fn.useEffect,Oh=Fn.useLayoutEffect,zh=Fn.useDebugValue;function Ih(e,t){var n=t(),r=Th({inst:{value:n,getSnapshot:t}}),s=r[0].inst,o=r[1];return Oh(function(){s.value=n,s.getSnapshot=t,nl(s)&&o({inst:s})},[e,n,t]),Lh(function(){return nl(s)&&o({inst:s}),e(function(){nl(s)&&o({inst:s})})},[e]),zh(n),n}function nl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Rh(e,n)}catch{return!0}}function Ah(e,t){return t()}var Mh=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Ah:Ih;yf.useSyncExternalStore=Fn.useSyncExternalStore!==void 0?Fn.useSyncExternalStore:Mh;gf.exports=yf;var Dh=gf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vo=M,Fh=Dh;function Uh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Bh=typeof Object.is=="function"?Object.is:Uh,$h=Fh.useSyncExternalStore,Hh=vo.useRef,Vh=vo.useEffect,Wh=vo.useMemo,Qh=vo.useDebugValue;hf.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var o=Hh(null);if(o.current===null){var l={hasValue:!1,value:null};o.current=l}else l=o.current;o=Wh(function(){function u(x){if(!c){if(c=!0,f=x,x=r(x),s!==void 0&&l.hasValue){var y=l.value;if(s(y,x))return m=y}return m=x}if(y=m,Bh(f,x))return y;var v=r(x);return s!==void 0&&s(y,v)?(f=x,y):(f=x,m=v)}var c=!1,f,m,g=n===void 0?null:n;return[function(){return u(t())},g===null?void 0:function(){return u(g())}]},[t,n,r,s]);var a=$h(e,o[0],o[1]);return Vh(function(){l.hasValue=!0,l.value=a},[a]),Qh(a),a};mf.exports=hf;var qh=mf.exports;const Gh=Yu(qh),{useDebugValue:Kh}=uc,{useSyncExternalStoreWithSelector:Jh}=Gh;const Xh=e=>e;function Yh(e,t=Xh,n){const r=Jh(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Kh(r),r}const Pu=e=>{const t=typeof e=="function"?Ch(e):e,n=(r,s)=>Yh(t,r,s);return Object.assign(n,t),n},la=e=>e?Pu(e):Pu;function xo(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var o;const l=u=>u===null?null:JSON.parse(u,t==null?void 0:t.reviver),a=(o=n.getItem(s))!=null?o:null;return a instanceof Promise?a.then(l):l(a)},setItem:(s,o)=>n.setItem(s,JSON.stringify(o,t==null?void 0:t.replacer)),removeItem:s=>n.removeItem(s)}}const zr=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return zr(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return zr(r)(n)}}}},Zh=(e,t)=>(n,r,s)=>{let o={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:w=>w,version:0,merge:(w,p)=>({...p,...w}),...t},l=!1;const a=new Set,u=new Set;let c;try{c=o.getStorage()}catch{}if(!c)return e((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...w)},r,s);const f=zr(o.serialize),m=()=>{const w=o.partialize({...r()});let p;const d=f({state:w,version:o.version}).then(h=>c.setItem(o.name,h)).catch(h=>{p=h});if(p)throw p;return d},g=s.setState;s.setState=(w,p)=>{g(w,p),m()};const x=e((...w)=>{n(...w),m()},r,s);let y;const v=()=>{var w;if(!c)return;l=!1,a.forEach(d=>d(r()));const p=((w=o.onRehydrateStorage)==null?void 0:w.call(o,r()))||void 0;return zr(c.getItem.bind(c))(o.name).then(d=>{if(d)return o.deserialize(d)}).then(d=>{if(d)if(typeof d.version=="number"&&d.version!==o.version){if(o.migrate)return o.migrate(d.state,d.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return d.state}).then(d=>{var h;return y=o.merge(d,(h=r())!=null?h:x),n(y,!0),m()}).then(()=>{p==null||p(y,void 0),l=!0,u.forEach(d=>d(y))}).catch(d=>{p==null||p(void 0,d)})};return s.persist={setOptions:w=>{o={...o,...w},w.getStorage&&(c=w.getStorage())},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>v(),hasHydrated:()=>l,onHydrate:w=>(a.add(w),()=>{a.delete(w)}),onFinishHydration:w=>(u.add(w),()=>{u.delete(w)})},v(),y||x},eg=(e,t)=>(n,r,s)=>{let o={storage:xo(()=>localStorage),partialize:v=>v,version:0,merge:(v,w)=>({...w,...v}),...t},l=!1;const a=new Set,u=new Set;let c=o.storage;if(!c)return e((...v)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...v)},r,s);const f=()=>{const v=o.partialize({...r()});return c.setItem(o.name,{state:v,version:o.version})},m=s.setState;s.setState=(v,w)=>{m(v,w),f()};const g=e((...v)=>{n(...v),f()},r,s);s.getInitialState=()=>g;let x;const y=()=>{var v,w;if(!c)return;l=!1,a.forEach(d=>{var h;return d((h=r())!=null?h:g)});const p=((w=o.onRehydrateStorage)==null?void 0:w.call(o,(v=r())!=null?v:g))||void 0;return zr(c.getItem.bind(c))(o.name).then(d=>{if(d)if(typeof d.version=="number"&&d.version!==o.version){if(o.migrate)return[!0,o.migrate(d.state,d.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,d.state];return[!1,void 0]}).then(d=>{var h;const[k,E]=d;if(x=o.merge(E,(h=r())!=null?h:g),n(x,!0),k)return f()}).then(()=>{p==null||p(x,void 0),x=r(),l=!0,u.forEach(d=>d(x))}).catch(d=>{p==null||p(void 0,d)})};return s.persist={setOptions:v=>{o={...o,...v},v.storage&&(c=v.storage)},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:v=>(a.add(v),()=>{a.delete(v)}),onFinishHydration:v=>(u.add(v),()=>{u.delete(v)})},o.skipHydration||y(),x||g},tg=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?Zh(e,t):eg(e,t),ia=tg;function vf(e,t){return function(){return e.apply(t,arguments)}}const{toString:ng}=Object.prototype,{getPrototypeOf:aa}=Object,{iterator:wo,toStringTag:xf}=Symbol,So=(e=>t=>{const n=ng.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),rt=e=>(e=e.toLowerCase(),t=>So(t)===e),ko=e=>t=>typeof t===e,{isArray:Hn}=Array,Ir=ko("undefined");function Br(e){return e!==null&&!Ir(e)&&e.constructor!==null&&!Ir(e.constructor)&&Oe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const wf=rt("ArrayBuffer");function rg(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&wf(e.buffer),t}const sg=ko("string"),Oe=ko("function"),Sf=ko("number"),$r=e=>e!==null&&typeof e=="object",og=e=>e===!0||e===!1,Ns=e=>{if(So(e)!=="object")return!1;const t=aa(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(xf in e)&&!(wo in e)},lg=e=>{if(!$r(e)||Br(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},ig=rt("Date"),ag=rt("File"),ug=rt("Blob"),cg=rt("FileList"),dg=e=>$r(e)&&Oe(e.pipe),fg=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Oe(e.append)&&((t=So(e))==="formdata"||t==="object"&&Oe(e.toString)&&e.toString()==="[object FormData]"))},pg=rt("URLSearchParams"),[mg,hg,gg,yg]=["ReadableStream","Request","Response","Headers"].map(rt),vg=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Hr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),Hn(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{if(Br(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),l=o.length;let a;for(r=0;r<l;r++)a=o[r],t.call(null,e[a],a,e)}}function kf(e,t){if(Br(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const Yt=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),jf=e=>!Ir(e)&&e!==Yt;function ri(){const{caseless:e}=jf(this)&&this||{},t={},n=(r,s)=>{const o=e&&kf(t,s)||s;Ns(t[o])&&Ns(r)?t[o]=ri(t[o],r):Ns(r)?t[o]=ri({},r):Hn(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&Hr(arguments[r],n);return t}const xg=(e,t,n,{allOwnKeys:r}={})=>(Hr(t,(s,o)=>{n&&Oe(s)?e[o]=vf(s,n):e[o]=s},{allOwnKeys:r}),e),wg=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Sg=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},kg=(e,t,n,r)=>{let s,o,l;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)l=s[o],(!r||r(l,e,t))&&!a[l]&&(t[l]=e[l],a[l]=!0);e=n!==!1&&aa(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},jg=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Ng=e=>{if(!e)return null;if(Hn(e))return e;let t=e.length;if(!Sf(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},_g=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&aa(Uint8Array)),Eg=(e,t)=>{const r=(e&&e[wo]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},bg=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Cg=rt("HTMLFormElement"),Pg=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Ru=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Rg=rt("RegExp"),Nf=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Hr(n,(s,o)=>{let l;(l=t(s,o,e))!==!1&&(r[o]=l||s)}),Object.defineProperties(e,r)},Tg=e=>{Nf(e,(t,n)=>{if(Oe(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Oe(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Lg=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return Hn(e)?r(e):r(String(e).split(t)),n},Og=()=>{},zg=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Ig(e){return!!(e&&Oe(e.append)&&e[xf]==="FormData"&&e[wo])}const Ag=e=>{const t=new Array(10),n=(r,s)=>{if($r(r)){if(t.indexOf(r)>=0)return;if(Br(r))return r;if(!("toJSON"in r)){t[s]=r;const o=Hn(r)?[]:{};return Hr(r,(l,a)=>{const u=n(l,s+1);!Ir(u)&&(o[a]=u)}),t[s]=void 0,o}}return r};return n(e,0)},Mg=rt("AsyncFunction"),Dg=e=>e&&($r(e)||Oe(e))&&Oe(e.then)&&Oe(e.catch),_f=((e,t)=>e?setImmediate:t?((n,r)=>(Yt.addEventListener("message",({source:s,data:o})=>{s===Yt&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),Yt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Oe(Yt.postMessage)),Fg=typeof queueMicrotask<"u"?queueMicrotask.bind(Yt):typeof process<"u"&&process.nextTick||_f,Ug=e=>e!=null&&Oe(e[wo]),S={isArray:Hn,isArrayBuffer:wf,isBuffer:Br,isFormData:fg,isArrayBufferView:rg,isString:sg,isNumber:Sf,isBoolean:og,isObject:$r,isPlainObject:Ns,isEmptyObject:lg,isReadableStream:mg,isRequest:hg,isResponse:gg,isHeaders:yg,isUndefined:Ir,isDate:ig,isFile:ag,isBlob:ug,isRegExp:Rg,isFunction:Oe,isStream:dg,isURLSearchParams:pg,isTypedArray:_g,isFileList:cg,forEach:Hr,merge:ri,extend:xg,trim:vg,stripBOM:wg,inherits:Sg,toFlatObject:kg,kindOf:So,kindOfTest:rt,endsWith:jg,toArray:Ng,forEachEntry:Eg,matchAll:bg,isHTMLForm:Cg,hasOwnProperty:Ru,hasOwnProp:Ru,reduceDescriptors:Nf,freezeMethods:Tg,toObjectSet:Lg,toCamelCase:Pg,noop:Og,toFiniteNumber:zg,findKey:kf,global:Yt,isContextDefined:jf,isSpecCompliantForm:Ig,toJSONObject:Ag,isAsyncFn:Mg,isThenable:Dg,setImmediate:_f,asap:Fg,isIterable:Ug};function A(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}S.inherits(A,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:S.toJSONObject(this.config),code:this.code,status:this.status}}});const Ef=A.prototype,bf={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{bf[e]={value:e}});Object.defineProperties(A,bf);Object.defineProperty(Ef,"isAxiosError",{value:!0});A.from=(e,t,n,r,s,o)=>{const l=Object.create(Ef);return S.toFlatObject(e,l,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),A.call(l,e.message,t,n,r,s),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const Bg=null;function si(e){return S.isPlainObject(e)||S.isArray(e)}function Cf(e){return S.endsWith(e,"[]")?e.slice(0,-2):e}function Tu(e,t,n){return e?e.concat(t).map(function(s,o){return s=Cf(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function $g(e){return S.isArray(e)&&!e.some(si)}const Hg=S.toFlatObject(S,{},null,function(t){return/^is[A-Z]/.test(t)});function jo(e,t,n){if(!S.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=S.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,w){return!S.isUndefined(w[v])});const r=n.metaTokens,s=n.visitor||f,o=n.dots,l=n.indexes,u=(n.Blob||typeof Blob<"u"&&Blob)&&S.isSpecCompliantForm(t);if(!S.isFunction(s))throw new TypeError("visitor must be a function");function c(y){if(y===null)return"";if(S.isDate(y))return y.toISOString();if(S.isBoolean(y))return y.toString();if(!u&&S.isBlob(y))throw new A("Blob is not supported. Use a Buffer instead.");return S.isArrayBuffer(y)||S.isTypedArray(y)?u&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function f(y,v,w){let p=y;if(y&&!w&&typeof y=="object"){if(S.endsWith(v,"{}"))v=r?v:v.slice(0,-2),y=JSON.stringify(y);else if(S.isArray(y)&&$g(y)||(S.isFileList(y)||S.endsWith(v,"[]"))&&(p=S.toArray(y)))return v=Cf(v),p.forEach(function(h,k){!(S.isUndefined(h)||h===null)&&t.append(l===!0?Tu([v],k,o):l===null?v:v+"[]",c(h))}),!1}return si(y)?!0:(t.append(Tu(w,v,o),c(y)),!1)}const m=[],g=Object.assign(Hg,{defaultVisitor:f,convertValue:c,isVisitable:si});function x(y,v){if(!S.isUndefined(y)){if(m.indexOf(y)!==-1)throw Error("Circular reference detected in "+v.join("."));m.push(y),S.forEach(y,function(p,d){(!(S.isUndefined(p)||p===null)&&s.call(t,p,S.isString(d)?d.trim():d,v,g))===!0&&x(p,v?v.concat(d):[d])}),m.pop()}}if(!S.isObject(e))throw new TypeError("data must be an object");return x(e),t}function Lu(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function ua(e,t){this._pairs=[],e&&jo(e,this,t)}const Pf=ua.prototype;Pf.append=function(t,n){this._pairs.push([t,n])};Pf.toString=function(t){const n=t?function(r){return t.call(this,r,Lu)}:Lu;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Vg(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rf(e,t,n){if(!t)return e;const r=n&&n.encode||Vg;S.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=S.isURLSearchParams(t)?t.toString():new ua(t,n).toString(r),o){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Wg{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){S.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ou=Wg,Tf={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Qg=typeof URLSearchParams<"u"?URLSearchParams:ua,qg=typeof FormData<"u"?FormData:null,Gg=typeof Blob<"u"?Blob:null,Kg={isBrowser:!0,classes:{URLSearchParams:Qg,FormData:qg,Blob:Gg},protocols:["http","https","file","blob","url","data"]},ca=typeof window<"u"&&typeof document<"u",oi=typeof navigator=="object"&&navigator||void 0,Jg=ca&&(!oi||["ReactNative","NativeScript","NS"].indexOf(oi.product)<0),Xg=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Yg=ca&&window.location.href||"http://localhost",Zg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ca,hasStandardBrowserEnv:Jg,hasStandardBrowserWebWorkerEnv:Xg,navigator:oi,origin:Yg},Symbol.toStringTag,{value:"Module"})),ke={...Zg,...Kg};function e0(e,t){return jo(e,new ke.classes.URLSearchParams,{visitor:function(n,r,s,o){return ke.isNode&&S.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function t0(e){return S.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function n0(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Lf(e){function t(n,r,s,o){let l=n[o++];if(l==="__proto__")return!0;const a=Number.isFinite(+l),u=o>=n.length;return l=!l&&S.isArray(s)?s.length:l,u?(S.hasOwnProp(s,l)?s[l]=[s[l],r]:s[l]=r,!a):((!s[l]||!S.isObject(s[l]))&&(s[l]=[]),t(n,r,s[l],o)&&S.isArray(s[l])&&(s[l]=n0(s[l])),!a)}if(S.isFormData(e)&&S.isFunction(e.entries)){const n={};return S.forEachEntry(e,(r,s)=>{t(t0(r),s,n,0)}),n}return null}function r0(e,t,n){if(S.isString(e))try{return(t||JSON.parse)(e),S.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const da={transitional:Tf,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=S.isObject(t);if(o&&S.isHTMLForm(t)&&(t=new FormData(t)),S.isFormData(t))return s?JSON.stringify(Lf(t)):t;if(S.isArrayBuffer(t)||S.isBuffer(t)||S.isStream(t)||S.isFile(t)||S.isBlob(t)||S.isReadableStream(t))return t;if(S.isArrayBufferView(t))return t.buffer;if(S.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return e0(t,this.formSerializer).toString();if((a=S.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return jo(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),r0(t)):t}],transformResponse:[function(t){const n=this.transitional||da.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(S.isResponse(t)||S.isReadableStream(t))return t;if(t&&S.isString(t)&&(r&&!this.responseType||s)){const l=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(l)throw a.name==="SyntaxError"?A.from(a,A.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ke.classes.FormData,Blob:ke.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};S.forEach(["delete","get","head","post","put","patch"],e=>{da.headers[e]={}});const fa=da,s0=S.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),o0=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(l){s=l.indexOf(":"),n=l.substring(0,s).trim().toLowerCase(),r=l.substring(s+1).trim(),!(!n||t[n]&&s0[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},zu=Symbol("internals");function nr(e){return e&&String(e).trim().toLowerCase()}function _s(e){return e===!1||e==null?e:S.isArray(e)?e.map(_s):String(e)}function l0(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const i0=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function rl(e,t,n,r,s){if(S.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!S.isString(t)){if(S.isString(r))return t.indexOf(r)!==-1;if(S.isRegExp(r))return r.test(t)}}function a0(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function u0(e,t){const n=S.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,l){return this[r].call(this,t,s,o,l)},configurable:!0})})}class No{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(a,u,c){const f=nr(u);if(!f)throw new Error("header name must be a non-empty string");const m=S.findKey(s,f);(!m||s[m]===void 0||c===!0||c===void 0&&s[m]!==!1)&&(s[m||u]=_s(a))}const l=(a,u)=>S.forEach(a,(c,f)=>o(c,f,u));if(S.isPlainObject(t)||t instanceof this.constructor)l(t,n);else if(S.isString(t)&&(t=t.trim())&&!i0(t))l(o0(t),n);else if(S.isObject(t)&&S.isIterable(t)){let a={},u,c;for(const f of t){if(!S.isArray(f))throw TypeError("Object iterator must return a key-value pair");a[c=f[0]]=(u=a[c])?S.isArray(u)?[...u,f[1]]:[u,f[1]]:f[1]}l(a,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=nr(t),t){const r=S.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return l0(s);if(S.isFunction(n))return n.call(this,s,r);if(S.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=nr(t),t){const r=S.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||rl(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(l){if(l=nr(l),l){const a=S.findKey(r,l);a&&(!n||rl(r,r[a],a,n))&&(delete r[a],s=!0)}}return S.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||rl(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return S.forEach(this,(s,o)=>{const l=S.findKey(r,o);if(l){n[l]=_s(s),delete n[o];return}const a=t?a0(o):String(o).trim();a!==o&&delete n[o],n[a]=_s(s),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return S.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&S.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[zu]=this[zu]={accessors:{}}).accessors,s=this.prototype;function o(l){const a=nr(l);r[a]||(u0(s,l),r[a]=!0)}return S.isArray(t)?t.forEach(o):o(t),this}}No.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);S.reduceDescriptors(No.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});S.freezeMethods(No);const tt=No;function sl(e,t){const n=this||fa,r=t||n,s=tt.from(r.headers);let o=r.data;return S.forEach(e,function(a){o=a.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Of(e){return!!(e&&e.__CANCEL__)}function Vn(e,t,n){A.call(this,e??"canceled",A.ERR_CANCELED,t,n),this.name="CanceledError"}S.inherits(Vn,A,{__CANCEL__:!0});function zf(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new A("Request failed with status code "+n.status,[A.ERR_BAD_REQUEST,A.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function c0(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function d0(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,l;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),f=r[o];l||(l=c),n[s]=u,r[s]=c;let m=o,g=0;for(;m!==s;)g+=n[m++],m=m%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-l<t)return;const x=f&&c-f;return x?Math.round(g*1e3/x):void 0}}function f0(e,t){let n=0,r=1e3/t,s,o;const l=(c,f=Date.now())=>{n=f,s=null,o&&(clearTimeout(o),o=null),e(...c)};return[(...c)=>{const f=Date.now(),m=f-n;m>=r?l(c,f):(s=c,o||(o=setTimeout(()=>{o=null,l(s)},r-m)))},()=>s&&l(s)]}const Ys=(e,t,n=3)=>{let r=0;const s=d0(50,250);return f0(o=>{const l=o.loaded,a=o.lengthComputable?o.total:void 0,u=l-r,c=s(u),f=l<=a;r=l;const m={loaded:l,total:a,progress:a?l/a:void 0,bytes:u,rate:c||void 0,estimated:c&&a&&f?(a-l)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(m)},n)},Iu=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Au=e=>(...t)=>S.asap(()=>e(...t)),p0=ke.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,ke.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(ke.origin),ke.navigator&&/(msie|trident)/i.test(ke.navigator.userAgent)):()=>!0,m0=ke.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const l=[e+"="+encodeURIComponent(t)];S.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),S.isString(r)&&l.push("path="+r),S.isString(s)&&l.push("domain="+s),o===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function h0(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function g0(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function If(e,t,n){let r=!h0(t);return e&&(r||n==!1)?g0(e,t):t}const Mu=e=>e instanceof tt?{...e}:e;function an(e,t){t=t||{};const n={};function r(c,f,m,g){return S.isPlainObject(c)&&S.isPlainObject(f)?S.merge.call({caseless:g},c,f):S.isPlainObject(f)?S.merge({},f):S.isArray(f)?f.slice():f}function s(c,f,m,g){if(S.isUndefined(f)){if(!S.isUndefined(c))return r(void 0,c,m,g)}else return r(c,f,m,g)}function o(c,f){if(!S.isUndefined(f))return r(void 0,f)}function l(c,f){if(S.isUndefined(f)){if(!S.isUndefined(c))return r(void 0,c)}else return r(void 0,f)}function a(c,f,m){if(m in t)return r(c,f);if(m in e)return r(void 0,c)}const u={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:a,headers:(c,f,m)=>s(Mu(c),Mu(f),m,!0)};return S.forEach(Object.keys({...e,...t}),function(f){const m=u[f]||s,g=m(e[f],t[f],f);S.isUndefined(g)&&m!==a||(n[f]=g)}),n}const Af=e=>{const t=an({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:l,auth:a}=t;t.headers=l=tt.from(l),t.url=Rf(If(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&l.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(S.isFormData(n)){if(ke.hasStandardBrowserEnv||ke.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((u=l.getContentType())!==!1){const[c,...f]=u?u.split(";").map(m=>m.trim()).filter(Boolean):[];l.setContentType([c||"multipart/form-data",...f].join("; "))}}if(ke.hasStandardBrowserEnv&&(r&&S.isFunction(r)&&(r=r(t)),r||r!==!1&&p0(t.url))){const c=s&&o&&m0.read(o);c&&l.set(s,c)}return t},y0=typeof XMLHttpRequest<"u",v0=y0&&function(e){return new Promise(function(n,r){const s=Af(e);let o=s.data;const l=tt.from(s.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:c}=s,f,m,g,x,y;function v(){x&&x(),y&&y(),s.cancelToken&&s.cancelToken.unsubscribe(f),s.signal&&s.signal.removeEventListener("abort",f)}let w=new XMLHttpRequest;w.open(s.method.toUpperCase(),s.url,!0),w.timeout=s.timeout;function p(){if(!w)return;const h=tt.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),E={data:!a||a==="text"||a==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:h,config:e,request:w};zf(function(P){n(P),v()},function(P){r(P),v()},E),w=null}"onloadend"in w?w.onloadend=p:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(p)},w.onabort=function(){w&&(r(new A("Request aborted",A.ECONNABORTED,e,w)),w=null)},w.onerror=function(){r(new A("Network Error",A.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let k=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const E=s.transitional||Tf;s.timeoutErrorMessage&&(k=s.timeoutErrorMessage),r(new A(k,E.clarifyTimeoutError?A.ETIMEDOUT:A.ECONNABORTED,e,w)),w=null},o===void 0&&l.setContentType(null),"setRequestHeader"in w&&S.forEach(l.toJSON(),function(k,E){w.setRequestHeader(E,k)}),S.isUndefined(s.withCredentials)||(w.withCredentials=!!s.withCredentials),a&&a!=="json"&&(w.responseType=s.responseType),c&&([g,y]=Ys(c,!0),w.addEventListener("progress",g)),u&&w.upload&&([m,x]=Ys(u),w.upload.addEventListener("progress",m),w.upload.addEventListener("loadend",x)),(s.cancelToken||s.signal)&&(f=h=>{w&&(r(!h||h.type?new Vn(null,e,w):h),w.abort(),w=null)},s.cancelToken&&s.cancelToken.subscribe(f),s.signal&&(s.signal.aborted?f():s.signal.addEventListener("abort",f)));const d=c0(s.url);if(d&&ke.protocols.indexOf(d)===-1){r(new A("Unsupported protocol "+d+":",A.ERR_BAD_REQUEST,e));return}w.send(o||null)})},x0=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(c){if(!s){s=!0,a();const f=c instanceof Error?c:this.reason;r.abort(f instanceof A?f:new Vn(f instanceof Error?f.message:f))}};let l=t&&setTimeout(()=>{l=null,o(new A(`timeout ${t} of ms exceeded`,A.ETIMEDOUT))},t);const a=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:u}=r;return u.unsubscribe=()=>S.asap(a),u}},w0=x0,S0=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},k0=async function*(e,t){for await(const n of j0(e))yield*S0(n,t)},j0=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Du=(e,t,n,r)=>{const s=k0(e,t);let o=0,l,a=u=>{l||(l=!0,r&&r(u))};return new ReadableStream({async pull(u){try{const{done:c,value:f}=await s.next();if(c){a(),u.close();return}let m=f.byteLength;if(n){let g=o+=m;n(g)}u.enqueue(new Uint8Array(f))}catch(c){throw a(c),c}},cancel(u){return a(u),s.return()}},{highWaterMark:2})},_o=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Mf=_o&&typeof ReadableStream=="function",N0=_o&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Df=(e,...t)=>{try{return!!e(...t)}catch{return!1}},_0=Mf&&Df(()=>{let e=!1;const t=new Request(ke.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Fu=64*1024,li=Mf&&Df(()=>S.isReadableStream(new Response("").body)),Zs={stream:li&&(e=>e.body)};_o&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Zs[t]&&(Zs[t]=S.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new A(`Response type '${t}' is not supported`,A.ERR_NOT_SUPPORT,r)})})})(new Response);const E0=async e=>{if(e==null)return 0;if(S.isBlob(e))return e.size;if(S.isSpecCompliantForm(e))return(await new Request(ke.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(S.isArrayBufferView(e)||S.isArrayBuffer(e))return e.byteLength;if(S.isURLSearchParams(e)&&(e=e+""),S.isString(e))return(await N0(e)).byteLength},b0=async(e,t)=>{const n=S.toFiniteNumber(e.getContentLength());return n??E0(t)},C0=_o&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:l,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:f,withCredentials:m="same-origin",fetchOptions:g}=Af(e);c=c?(c+"").toLowerCase():"text";let x=w0([s,o&&o.toAbortSignal()],l),y;const v=x&&x.unsubscribe&&(()=>{x.unsubscribe()});let w;try{if(u&&_0&&n!=="get"&&n!=="head"&&(w=await b0(f,r))!==0){let E=new Request(t,{method:"POST",body:r,duplex:"half"}),b;if(S.isFormData(r)&&(b=E.headers.get("content-type"))&&f.setContentType(b),E.body){const[P,N]=Iu(w,Ys(Au(u)));r=Du(E.body,Fu,P,N)}}S.isString(m)||(m=m?"include":"omit");const p="credentials"in Request.prototype;y=new Request(t,{...g,signal:x,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:p?m:void 0});let d=await fetch(y,g);const h=li&&(c==="stream"||c==="response");if(li&&(a||h&&v)){const E={};["status","statusText","headers"].forEach(C=>{E[C]=d[C]});const b=S.toFiniteNumber(d.headers.get("content-length")),[P,N]=a&&Iu(b,Ys(Au(a),!0))||[];d=new Response(Du(d.body,Fu,P,()=>{N&&N(),v&&v()}),E)}c=c||"text";let k=await Zs[S.findKey(Zs,c)||"text"](d,e);return!h&&v&&v(),await new Promise((E,b)=>{zf(E,b,{data:k,headers:tt.from(d.headers),status:d.status,statusText:d.statusText,config:e,request:y})})}catch(p){throw v&&v(),p&&p.name==="TypeError"&&/Load failed|fetch/i.test(p.message)?Object.assign(new A("Network Error",A.ERR_NETWORK,e,y),{cause:p.cause||p}):A.from(p,p&&p.code,e,y)}}),ii={http:Bg,xhr:v0,fetch:C0};S.forEach(ii,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Uu=e=>`- ${e}`,P0=e=>S.isFunction(e)||e===null||e===!1,Ff={getAdapter:e=>{e=S.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let l;if(r=n,!P0(n)&&(r=ii[(l=String(n)).toLowerCase()],r===void 0))throw new A(`Unknown adapter '${l}'`);if(r)break;s[l||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let l=t?o.length>1?`since :
`+o.map(Uu).join(`
`):" "+Uu(o[0]):"as no adapter specified";throw new A("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return r},adapters:ii};function ol(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Vn(null,e)}function Bu(e){return ol(e),e.headers=tt.from(e.headers),e.data=sl.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ff.getAdapter(e.adapter||fa.adapter)(e).then(function(r){return ol(e),r.data=sl.call(e,e.transformResponse,r),r.headers=tt.from(r.headers),r},function(r){return Of(r)||(ol(e),r&&r.response&&(r.response.data=sl.call(e,e.transformResponse,r.response),r.response.headers=tt.from(r.response.headers))),Promise.reject(r)})}const Uf="1.11.0",Eo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Eo[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const $u={};Eo.transitional=function(t,n,r){function s(o,l){return"[Axios v"+Uf+"] Transitional option '"+o+"'"+l+(r?". "+r:"")}return(o,l,a)=>{if(t===!1)throw new A(s(l," has been removed"+(n?" in "+n:"")),A.ERR_DEPRECATED);return n&&!$u[l]&&($u[l]=!0,console.warn(s(l," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,l,a):!0}};Eo.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function R0(e,t,n){if(typeof e!="object")throw new A("options must be an object",A.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],l=t[o];if(l){const a=e[o],u=a===void 0||l(a,o,e);if(u!==!0)throw new A("option "+o+" must be "+u,A.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new A("Unknown option "+o,A.ERR_BAD_OPTION)}}const Es={assertOptions:R0,validators:Eo},lt=Es.validators;class eo{constructor(t){this.defaults=t||{},this.interceptors={request:new Ou,response:new Ou}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=an(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&Es.assertOptions(r,{silentJSONParsing:lt.transitional(lt.boolean),forcedJSONParsing:lt.transitional(lt.boolean),clarifyTimeoutError:lt.transitional(lt.boolean)},!1),s!=null&&(S.isFunction(s)?n.paramsSerializer={serialize:s}:Es.assertOptions(s,{encode:lt.function,serialize:lt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Es.assertOptions(n,{baseUrl:lt.spelling("baseURL"),withXsrfToken:lt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let l=o&&S.merge(o.common,o[n.method]);o&&S.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=tt.concat(l,o);const a=[];let u=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(u=u&&v.synchronous,a.unshift(v.fulfilled,v.rejected))});const c=[];this.interceptors.response.forEach(function(v){c.push(v.fulfilled,v.rejected)});let f,m=0,g;if(!u){const y=[Bu.bind(this),void 0];for(y.unshift(...a),y.push(...c),g=y.length,f=Promise.resolve(n);m<g;)f=f.then(y[m++],y[m++]);return f}g=a.length;let x=n;for(m=0;m<g;){const y=a[m++],v=a[m++];try{x=y(x)}catch(w){v.call(this,w);break}}try{f=Bu.call(this,x)}catch(y){return Promise.reject(y)}for(m=0,g=c.length;m<g;)f=f.then(c[m++],c[m++]);return f}getUri(t){t=an(this.defaults,t);const n=If(t.baseURL,t.url,t.allowAbsoluteUrls);return Rf(n,t.params,t.paramsSerializer)}}S.forEach(["delete","get","head","options"],function(t){eo.prototype[t]=function(n,r){return this.request(an(r||{},{method:t,url:n,data:(r||{}).data}))}});S.forEach(["post","put","patch"],function(t){function n(r){return function(o,l,a){return this.request(an(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:l}))}}eo.prototype[t]=n(),eo.prototype[t+"Form"]=n(!0)});const bs=eo;class pa{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const l=new Promise(a=>{r.subscribe(a),o=a}).then(s);return l.cancel=function(){r.unsubscribe(o)},l},t(function(o,l,a){r.reason||(r.reason=new Vn(o,l,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new pa(function(s){t=s}),cancel:t}}}const T0=pa;function L0(e){return function(n){return e.apply(null,n)}}function O0(e){return S.isObject(e)&&e.isAxiosError===!0}const ai={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ai).forEach(([e,t])=>{ai[t]=e});const z0=ai;function Bf(e){const t=new bs(e),n=vf(bs.prototype.request,t);return S.extend(n,bs.prototype,t,{allOwnKeys:!0}),S.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Bf(an(e,s))},n}const ae=Bf(fa);ae.Axios=bs;ae.CanceledError=Vn;ae.CancelToken=T0;ae.isCancel=Of;ae.VERSION=Uf;ae.toFormData=jo;ae.AxiosError=A;ae.Cancel=ae.CanceledError;ae.all=function(t){return Promise.all(t)};ae.spread=L0;ae.isAxiosError=O0;ae.mergeConfig=an;ae.AxiosHeaders=tt;ae.formToJSON=e=>Lf(S.isHTMLForm(e)?new FormData(e):e);ae.getAdapter=Ff.getAdapter;ae.HttpStatusCode=z0;ae.default=ae;const I0=ae;class A0{constructor(){Po(this,"client");Po(this,"config");this.config=this.getWPConfig(),this.client=I0.create({baseURL:this.config.ajaxUrl,timeout:6e4,headers:{"Content-Type":"application/x-www-form-urlencoded"}}),this.setupInterceptors()}getWPConfig(){if(typeof window<"u"&&window.wpNotionConfig)return window.wpNotionConfig;if(typeof window<"u"&&window.notionToWp)return window.notionToWp;throw new Error("WordPress配置未找到，请确保页面已正确加载")}setupInterceptors(){this.client.interceptors.request.use(t=>{if(t.data||(t.data={}),t.data instanceof FormData)t.data.append("nonce",this.config.nonce);else{const n=new URLSearchParams;typeof t.data=="object"&&Object.entries(t.data).forEach(([r,s])=>{s!=null&&n.append(r,String(s))}),n.append("nonce",this.config.nonce),t.data=n}return t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>{var r;const n=t.data;if(typeof n!="object"||typeof n.success!="boolean")throw new Error("无效的响应格式");if(!n.success){const s=n,o=typeof s.data=="string"?s.data:((r=s.data)==null?void 0:r.message)||"未知错误";throw new Error(o)}return t},t=>{if(t.code==="ECONNABORTED")throw new Error("请求超时，请检查网络连接");if(t.response){const n=t.response.status;if(n===403)throw new Error("权限不足，请刷新页面重试");if(n===404)throw new Error("请求的资源不存在");if(n>=500)throw new Error("服务器内部错误，请稍后重试")}throw t})}async request(t,n={},r={}){try{return(await this.client.post("",{action:t,...n},r)).data.data}catch(s){throw s}}async startSync(t={}){return this.request("notion_to_wordpress_manual_sync",t)}async cancelSync(t){return this.request("notion_to_wordpress_cancel_sync",{task_id:t})}async retryFailed(){return this.request("notion_to_wordpress_retry_failed")}async getSyncStatus(){return this.request("notion_to_wordpress_get_async_status")}async testConnection(t){return this.request("notion_to_wordpress_test_connection",t)}async getStats(){return this.request("notion_to_wordpress_get_stats")}async refreshPerformanceStats(){return this.request("notion_to_wordpress_refresh_performance_stats")}async resetPerformanceStats(){return this.request("notion_to_wordpress_reset_performance_stats")}async getSettings(){return this.request("notion_to_wordpress_get_settings")}async saveSettings(t){return this.request("notion_to_wordpress_save_settings",t)}async viewLog(t){return this.request("notion_to_wordpress_view_log",t)}async clearLogs(){return this.request("notion_to_wordpress_clear_logs")}async getSystemInfo(){return this.request("notion_to_wordpress_test_debug")}async getIndexStatus(){return this.request("notion_to_wordpress_get_index_status")}async createDatabaseIndexes(){return this.request("notion_to_wordpress_create_database_indexes")}async optimizeAllIndexes(){return this.request("notion_to_wordpress_optimize_all_indexes")}async removeDatabaseIndexes(){return this.request("notion_to_wordpress_remove_database_indexes")}async analyzeQueryPerformance(){return this.request("notion_to_wordpress_analyze_query_performance")}async getQueueStatus(){return this.request("notion_to_wordpress_get_queue_status")}async controlAsyncOperation(t){return this.request("notion_to_wordpress_control_async_operation",{action_type:t})}async cleanupQueue(){return this.request("notion_to_wordpress_cleanup_queue")}async cancelQueueTask(t){return this.request("notion_to_wordpress_cancel_queue_task",{task_id:t})}async getSmartRecommendations(){return this.request("notion_to_wordpress_get_smart_recommendations")}async refreshVerificationToken(){return this.request("notion_to_wordpress_refresh_verification_token")}createSSEConnection(t){const n=new URL(this.config.ajaxUrl);return n.searchParams.append("action","notion_to_wordpress_sse_progress"),n.searchParams.append("nonce",this.config.nonce),t&&n.searchParams.append("task_id",t),new EventSource(n.toString())}async requestWithRetry(t,n={},r=3,s=1e3){let o;for(let l=1;l<=r;l++)try{return await this.request(t,n)}catch(a){if(o=a,l===r||o.message.includes("权限")||o.message.includes("nonce"))break;await new Promise(u=>setTimeout(u,s*l))}throw o}async batchRequest(t){const n=t.map(({action:r,data:s})=>this.request(r,s).catch(o=>({error:o.message})));return Promise.all(n)}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let ll=null;function Lt(){return ll||(ll=new A0),ll}const Hu={isRunning:!1,progress:0,status:"idle",currentStep:"",taskId:null,syncType:null,startTime:null,stats:null,sseConnected:!1,sseEventSource:null,error:null,lastError:null},Wn=la()(ia((e,t)=>({...Hu,startSync:async n=>{var r;try{e({isRunning:!0,status:"running",progress:0,currentStep:"准备同步...",syncType:n.type||"manual",startTime:Date.now(),error:null});const o=await Lt().startSync(n);if(o.success&&((r=o.data)!=null&&r.taskId)){const l=o.data.taskId;e({taskId:l,currentStep:"同步已启动"}),t().connectSSE(l)}else throw new Error(o.message||"启动同步失败")}catch(s){const o=s instanceof Error?s.message:"启动同步时发生未知错误";e({isRunning:!1,status:"failed",error:o,lastError:o}),console.error("❌ [同步Store] 启动同步失败:",s)}},stopSync:async()=>{try{const{taskId:n}=t();if(!n)return;await Lt().cancelSync(n),t().disconnectSSE(),e({isRunning:!1,status:"cancelled",currentStep:"同步已停止"})}catch(n){console.error("❌ [同步Store] 停止同步失败:",n),t().setError("停止同步失败")}},cancelSync:async()=>{await t().stopSync()},retryFailedSync:async()=>{const{syncType:n}=t();n&&(e({error:null,lastError:null}),await t().startSync({type:n}))},updateProgress:(n,r)=>{e(s=>({progress:Math.max(0,Math.min(100,n)),currentStep:r||s.currentStep}))},updateStatus:(n,r)=>{e(s=>({status:n,isRunning:n==="running",currentStep:(r==null?void 0:r.message)||s.currentStep,progress:(r==null?void 0:r.progress)!==void 0?r.progress:s.progress})),(n==="completed"||n==="failed"||n==="cancelled")&&(t().disconnectSSE(),n==="completed"&&setTimeout(()=>t().loadStats(),1e3))},loadStats:async()=>{try{const r=await Lt().getStats();e({stats:r})}catch(n){console.error("❌ [同步Store] 加载统计数据失败:",n)}},updateStats:n=>{e(r=>({stats:r.stats?{...r.stats,...n}:null}))},connectSSE:n=>{t().disconnectSSE();try{const s=Lt().createSSEConnection(n);s.onopen=()=>{e({sseConnected:!0,sseEventSource:s}),console.log("🔗 [同步Store] SSE连接已建立")},s.onmessage=o=>{try{const l=JSON.parse(o.data);t().handleSSEEvent(l)}catch(l){console.error("❌ [同步Store] SSE消息解析失败:",l)}},s.onerror=o=>{console.error("❌ [同步Store] SSE连接错误:",o),t().disconnectSSE()}}catch(r){console.error("❌ [同步Store] 创建SSE连接失败:",r)}},disconnectSSE:()=>{const{sseEventSource:n}=t();n&&(n.close(),e({sseConnected:!1,sseEventSource:null}),console.log("🔌 [同步Store] SSE连接已断开"))},handleSSEEvent:n=>{const{type:r,data:s}=n;switch(r){case"progress":s.progress!==void 0&&t().updateProgress(s.progress,s.message);break;case"status":s.status&&t().updateStatus(s.status,{step:"status_update",status:"running",message:s.message||"状态更新",timestamp:new Date().toISOString(),progress:s.progress});break;case"stats":s.stats&&t().updateStats(s.stats);break;case"error":t().setError(s.message||"同步过程中发生错误");break;case"complete":t().updateStatus("completed",{step:"complete",status:"completed",message:s.message||"同步完成",timestamp:new Date().toISOString(),progress:100});break;default:console.log("📨 [同步Store] 收到未知SSE事件:",n)}},setError:n=>{e({error:n,lastError:n})},clearError:()=>{e({error:null})},reset:()=>{t().disconnectSSE(),e(Hu)}}),{name:"notion-wp-sync-store",storage:xo(()=>localStorage),partialize:e=>({taskId:e.taskId,syncType:e.syncType,startTime:e.startTime,stats:e.stats,lastError:e.lastError})}));function $f(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=$f(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function M0(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=$f(e))&&(r&&(r+=" "),r+=t);return r}const ma="-",D0=e=>{const t=U0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const a=l.split(ma);return a[0]===""&&a.length!==1&&a.shift(),Hf(a,t)||F0(l)},getConflictingClassGroupIds:(l,a)=>{const u=n[l]||[];return a&&r[l]?[...u,...r[l]]:u}}},Hf=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?Hf(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const o=e.join(ma);return(l=t.validators.find(({validator:a})=>a(o)))==null?void 0:l.classGroupId},Vu=/^\[(.+)\]$/,F0=e=>{if(Vu.test(e)){const t=Vu.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},U0=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const s in n)ui(n[s],r,s,t);return r},ui=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const o=s===""?t:Wu(t,s);o.classGroupId=n;return}if(typeof s=="function"){if(B0(s)){ui(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([o,l])=>{ui(l,Wu(t,o),n,r)})})},Wu=(e,t)=>{let n=e;return t.split(ma).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},B0=e=>e.isThemeGetter,$0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(o,l)=>{n.set(o,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let l=n.get(o);if(l!==void 0)return l;if((l=r.get(o))!==void 0)return s(o,l),l},set(o,l){n.has(o)?n.set(o,l):s(o,l)}}},ci="!",di=":",H0=di.length,V0=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=s=>{const o=[];let l=0,a=0,u=0,c;for(let y=0;y<s.length;y++){let v=s[y];if(l===0&&a===0){if(v===di){o.push(s.slice(u,y)),u=y+H0;continue}if(v==="/"){c=y;continue}}v==="["?l++:v==="]"?l--:v==="("?a++:v===")"&&a--}const f=o.length===0?s:s.substring(u),m=W0(f),g=m!==f,x=c&&c>u?c-u:void 0;return{modifiers:o,hasImportantModifier:g,baseClassName:m,maybePostfixModifierPosition:x}};if(t){const s=t+di,o=r;r=l=>l.startsWith(s)?o(l.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(n){const s=r;r=o=>n({className:o,parseClassName:s})}return r},W0=e=>e.endsWith(ci)?e.substring(0,e.length-1):e.startsWith(ci)?e.substring(1):e,Q0=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let o=[];return r.forEach(l=>{l[0]==="["||t[l]?(s.push(...o.sort(),l),o=[]):o.push(l)}),s.push(...o.sort()),s}},q0=e=>({cache:$0(e.cacheSize),parseClassName:V0(e),sortModifiers:Q0(e),...D0(e)}),G0=/\s+/,K0=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:o}=t,l=[],a=e.trim().split(G0);let u="";for(let c=a.length-1;c>=0;c-=1){const f=a[c],{isExternal:m,modifiers:g,hasImportantModifier:x,baseClassName:y,maybePostfixModifierPosition:v}=n(f);if(m){u=f+(u.length>0?" "+u:u);continue}let w=!!v,p=r(w?y.substring(0,v):y);if(!p){if(!w){u=f+(u.length>0?" "+u:u);continue}if(p=r(y),!p){u=f+(u.length>0?" "+u:u);continue}w=!1}const d=o(g).join(":"),h=x?d+ci:d,k=h+p;if(l.includes(k))continue;l.push(k);const E=s(p,w);for(let b=0;b<E.length;++b){const P=E[b];l.push(h+P)}u=f+(u.length>0?" "+u:u)}return u};function J0(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Vf(t))&&(r&&(r+=" "),r+=n);return r}const Vf=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Vf(e[r]))&&(n&&(n+=" "),n+=t);return n};function X0(e,...t){let n,r,s,o=l;function l(u){const c=t.reduce((f,m)=>m(f),e());return n=q0(c),r=n.cache.get,s=n.cache.set,o=a,a(u)}function a(u){const c=r(u);if(c)return c;const f=K0(u,n);return s(u,f),f}return function(){return o(J0.apply(null,arguments))}}const ce=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Wf=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Qf=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Y0=/^\d+\/\d+$/,Z0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ey=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ty=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,ny=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ry=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pn=e=>Y0.test(e),F=e=>!!e&&!Number.isNaN(Number(e)),_t=e=>!!e&&Number.isInteger(Number(e)),il=e=>e.endsWith("%")&&F(e.slice(0,-1)),dt=e=>Z0.test(e),sy=()=>!0,oy=e=>ey.test(e)&&!ty.test(e),qf=()=>!1,ly=e=>ny.test(e),iy=e=>ry.test(e),ay=e=>!T(e)&&!L(e),uy=e=>Qn(e,Jf,qf),T=e=>Wf.test(e),Qt=e=>Qn(e,Xf,oy),al=e=>Qn(e,my,F),Qu=e=>Qn(e,Gf,qf),cy=e=>Qn(e,Kf,iy),cs=e=>Qn(e,Yf,ly),L=e=>Qf.test(e),rr=e=>qn(e,Xf),dy=e=>qn(e,hy),qu=e=>qn(e,Gf),fy=e=>qn(e,Jf),py=e=>qn(e,Kf),ds=e=>qn(e,Yf,!0),Qn=(e,t,n)=>{const r=Wf.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},qn=(e,t,n=!1)=>{const r=Qf.exec(e);return r?r[1]?t(r[1]):n:!1},Gf=e=>e==="position"||e==="percentage",Kf=e=>e==="image"||e==="url",Jf=e=>e==="length"||e==="size"||e==="bg-size",Xf=e=>e==="length",my=e=>e==="number",hy=e=>e==="family-name",Yf=e=>e==="shadow",gy=()=>{const e=ce("color"),t=ce("font"),n=ce("text"),r=ce("font-weight"),s=ce("tracking"),o=ce("leading"),l=ce("breakpoint"),a=ce("container"),u=ce("spacing"),c=ce("radius"),f=ce("shadow"),m=ce("inset-shadow"),g=ce("text-shadow"),x=ce("drop-shadow"),y=ce("blur"),v=ce("perspective"),w=ce("aspect"),p=ce("ease"),d=ce("animate"),h=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...k(),L,T],b=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],N=()=>[L,T,u],C=()=>[pn,"full","auto",...N()],O=()=>[_t,"none","subgrid",L,T],ue=()=>["auto",{span:["full",_t,L,T]},_t,L,T],ve=()=>[_t,"auto",L,T],ze=()=>["auto","min","max","fr",L,T],dn=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],kt=()=>["start","end","center","stretch","center-safe","end-safe"],Ie=()=>["auto",...N()],Ge=()=>[pn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],j=()=>[e,L,T],z=()=>[...k(),qu,Qu,{position:[L,T]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],J=()=>["auto","cover","contain",fy,uy,{size:[L,T]}],ne=()=>[il,rr,Qt],le=()=>["","none","full",c,L,T],se=()=>["",F,rr,Qt],jt=()=>["solid","dashed","dotted","double"],st=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Y=()=>[F,il,qu,Qu],va=()=>["","none",y,L,T],Vr=()=>["none",F,L,T],Wr=()=>["none",F,L,T],Co=()=>[F,L,T],Qr=()=>[pn,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[dt],breakpoint:[dt],color:[sy],container:[dt],"drop-shadow":[dt],ease:["in","out","in-out"],font:[ay],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[dt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[dt],shadow:[dt],spacing:["px",F],text:[dt],"text-shadow":[dt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",pn,T,L,w]}],container:["container"],columns:[{columns:[F,T,L,a]}],"break-after":[{"break-after":h()}],"break-before":[{"break-before":h()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:b()}],"overflow-x":[{"overflow-x":b()}],"overflow-y":[{"overflow-y":b()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[_t,"auto",L,T]}],basis:[{basis:[pn,"full","auto",a,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[F,pn,"auto","initial","none",T]}],grow:[{grow:["",F,L,T]}],shrink:[{shrink:["",F,L,T]}],order:[{order:[_t,"first","last","none",L,T]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:ue()}],"col-start":[{"col-start":ve()}],"col-end":[{"col-end":ve()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:ue()}],"row-start":[{"row-start":ve()}],"row-end":[{"row-end":ve()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ze()}],"auto-rows":[{"auto-rows":ze()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...dn(),"normal"]}],"justify-items":[{"justify-items":[...kt(),"normal"]}],"justify-self":[{"justify-self":["auto",...kt()]}],"align-content":[{content:["normal",...dn()]}],"align-items":[{items:[...kt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...kt(),{baseline:["","last"]}]}],"place-content":[{"place-content":dn()}],"place-items":[{"place-items":[...kt(),"baseline"]}],"place-self":[{"place-self":["auto",...kt()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:Ie()}],mx:[{mx:Ie()}],my:[{my:Ie()}],ms:[{ms:Ie()}],me:[{me:Ie()}],mt:[{mt:Ie()}],mr:[{mr:Ie()}],mb:[{mb:Ie()}],ml:[{ml:Ie()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:Ge()}],w:[{w:[a,"screen",...Ge()]}],"min-w":[{"min-w":[a,"screen","none",...Ge()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[l]},...Ge()]}],h:[{h:["screen","lh",...Ge()]}],"min-h":[{"min-h":["screen","lh","none",...Ge()]}],"max-h":[{"max-h":["screen","lh",...Ge()]}],"font-size":[{text:["base",n,rr,Qt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,L,al]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",il,T]}],"font-family":[{font:[dy,T,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,L,T]}],"line-clamp":[{"line-clamp":[F,"none",L,al]}],leading:[{leading:[o,...N()]}],"list-image":[{"list-image":["none",L,T]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,T]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...jt(),"wavy"]}],"text-decoration-thickness":[{decoration:[F,"from-font","auto",L,Qt]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[F,"auto",L,T]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,T]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,T]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:z()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:J()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_t,L,T],radial:["",L,T],conic:[_t,L,T]},py,cy]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:ne()}],"gradient-via-pos":[{via:ne()}],"gradient-to-pos":[{to:ne()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:le()}],"rounded-s":[{"rounded-s":le()}],"rounded-e":[{"rounded-e":le()}],"rounded-t":[{"rounded-t":le()}],"rounded-r":[{"rounded-r":le()}],"rounded-b":[{"rounded-b":le()}],"rounded-l":[{"rounded-l":le()}],"rounded-ss":[{"rounded-ss":le()}],"rounded-se":[{"rounded-se":le()}],"rounded-ee":[{"rounded-ee":le()}],"rounded-es":[{"rounded-es":le()}],"rounded-tl":[{"rounded-tl":le()}],"rounded-tr":[{"rounded-tr":le()}],"rounded-br":[{"rounded-br":le()}],"rounded-bl":[{"rounded-bl":le()}],"border-w":[{border:se()}],"border-w-x":[{"border-x":se()}],"border-w-y":[{"border-y":se()}],"border-w-s":[{"border-s":se()}],"border-w-e":[{"border-e":se()}],"border-w-t":[{"border-t":se()}],"border-w-r":[{"border-r":se()}],"border-w-b":[{"border-b":se()}],"border-w-l":[{"border-l":se()}],"divide-x":[{"divide-x":se()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":se()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...jt(),"hidden","none"]}],"divide-style":[{divide:[...jt(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...jt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[F,L,T]}],"outline-w":[{outline:["",F,rr,Qt]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",f,ds,cs]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",m,ds,cs]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:se()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[F,Qt]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":se()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",g,ds,cs]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[F,L,T]}],"mix-blend":[{"mix-blend":[...st(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":st()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[F]}],"mask-image-linear-from-pos":[{"mask-linear-from":Y()}],"mask-image-linear-to-pos":[{"mask-linear-to":Y()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":Y()}],"mask-image-t-to-pos":[{"mask-t-to":Y()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":Y()}],"mask-image-r-to-pos":[{"mask-r-to":Y()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":Y()}],"mask-image-b-to-pos":[{"mask-b-to":Y()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":Y()}],"mask-image-l-to-pos":[{"mask-l-to":Y()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":Y()}],"mask-image-x-to-pos":[{"mask-x-to":Y()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":Y()}],"mask-image-y-to-pos":[{"mask-y-to":Y()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[L,T]}],"mask-image-radial-from-pos":[{"mask-radial-from":Y()}],"mask-image-radial-to-pos":[{"mask-radial-to":Y()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[F]}],"mask-image-conic-from-pos":[{"mask-conic-from":Y()}],"mask-image-conic-to-pos":[{"mask-conic-to":Y()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:z()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:J()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,T]}],filter:[{filter:["","none",L,T]}],blur:[{blur:va()}],brightness:[{brightness:[F,L,T]}],contrast:[{contrast:[F,L,T]}],"drop-shadow":[{"drop-shadow":["","none",x,ds,cs]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",F,L,T]}],"hue-rotate":[{"hue-rotate":[F,L,T]}],invert:[{invert:["",F,L,T]}],saturate:[{saturate:[F,L,T]}],sepia:[{sepia:["",F,L,T]}],"backdrop-filter":[{"backdrop-filter":["","none",L,T]}],"backdrop-blur":[{"backdrop-blur":va()}],"backdrop-brightness":[{"backdrop-brightness":[F,L,T]}],"backdrop-contrast":[{"backdrop-contrast":[F,L,T]}],"backdrop-grayscale":[{"backdrop-grayscale":["",F,L,T]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[F,L,T]}],"backdrop-invert":[{"backdrop-invert":["",F,L,T]}],"backdrop-opacity":[{"backdrop-opacity":[F,L,T]}],"backdrop-saturate":[{"backdrop-saturate":[F,L,T]}],"backdrop-sepia":[{"backdrop-sepia":["",F,L,T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,T]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[F,"initial",L,T]}],ease:[{ease:["linear","initial",p,L,T]}],delay:[{delay:[F,L,T]}],animate:[{animate:["none",d,L,T]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,L,T]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:Vr()}],"rotate-x":[{"rotate-x":Vr()}],"rotate-y":[{"rotate-y":Vr()}],"rotate-z":[{"rotate-z":Vr()}],scale:[{scale:Wr()}],"scale-x":[{"scale-x":Wr()}],"scale-y":[{"scale-y":Wr()}],"scale-z":[{"scale-z":Wr()}],"scale-3d":["scale-3d"],skew:[{skew:Co()}],"skew-x":[{"skew-x":Co()}],"skew-y":[{"skew-y":Co()}],transform:[{transform:[L,T,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Qr()}],"translate-x":[{"translate-x":Qr()}],"translate-y":[{"translate-y":Qr()}],"translate-z":[{"translate-z":Qr()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,T]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,T]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[F,rr,Qt,al]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},yy=X0(gy);function K(...e){return yy(M0(e))}const vy={primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",info:"bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-400",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500",outline:"border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500",link:"bg-transparent text-blue-600 hover:text-blue-700 hover:underline focus:ring-blue-500 p-0"},xy={xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"},I=M.forwardRef(({variant:e="primary",size:t="md",fullWidth:n=!1,icon:r,iconPosition:s="left",loading:o=!1,disabled:l=!1,className:a,children:u,...c},f)=>{const m=l||o;return i.jsxs("button",{ref:f,className:K("inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed",vy[e],e!=="link"&&xy[t],n&&"w-full",o&&"cursor-wait",a),disabled:m,...c,children:[r&&s==="left"||o?i.jsx("span",{className:K("flex items-center",u&&"mr-2"),children:o?i.jsxs("svg",{className:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):r}):null,u,r&&s==="right"&&!o?i.jsx("span",{className:K("flex items-center",u&&"ml-2"),children:r}):null]})});I.displayName="Button";const wy={xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-3 py-2 text-sm",lg:"px-4 py-3 text-base",xl:"px-5 py-4 text-lg"},Sy={idle:"border-gray-300 focus:border-blue-500 focus:ring-blue-500",loading:"border-gray-300 focus:border-blue-500 focus:ring-blue-500",success:"border-green-500 focus:border-green-500 focus:ring-green-500",error:"border-red-500 focus:border-red-500 focus:ring-red-500"},q=M.forwardRef(({type:e="text",size:t="md",status:n="idle",label:r,helperText:s,errorText:o,leftIcon:l,rightIcon:a,fullWidth:u=!1,loading:c=!1,disabled:f=!1,className:m,...g},x)=>{const y=g.id||`input-${Math.random().toString(36).substring(2,9)}`,v=n==="error"||!!o,w=v?"error":n,p=f||c;return i.jsxs("div",{className:K("flex flex-col",u&&"w-full"),children:[r&&i.jsx("label",{htmlFor:y,className:"block text-sm font-medium text-gray-700 mb-1",children:r}),i.jsxs("div",{className:"relative",children:[l&&i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx("span",{className:"text-gray-400",children:l})}),i.jsx("input",{ref:x,id:y,type:e,className:K("block w-full border rounded-md shadow-sm transition-colors duration-200","placeholder-gray-400 focus:outline-none focus:ring-1","disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",wy[t],Sy[w],l&&"pl-10",a&&"pr-10",m),disabled:p,...g}),(a||c)&&i.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:c?i.jsxs("svg",{className:"animate-spin h-4 w-4 text-gray-400",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):i.jsx("span",{className:"text-gray-400",children:a})})]}),(s||o)&&i.jsx("p",{className:K("mt-1 text-xs",v?"text-red-600":"text-gray-500"),children:o||s})]})});q.displayName="Input";const ha={none:"",sm:"p-3",md:"p-4",lg:"p-6"},ky={none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"},$=({title:e,subtitle:t,actions:n,footer:r,padding:s="md",shadow:o="sm",border:l=!0,hover:a=!1,className:u,children:c,...f})=>i.jsxs("div",{className:K("bg-white rounded-lg transition-shadow duration-200",l&&"border border-gray-200",ky[o],a&&"hover:shadow-md",u),...f,children:[(e||t||n)&&i.jsxs("div",{className:K("flex items-start justify-between",s!=="none"&&"px-4 py-3 border-b border-gray-200"),children:[i.jsxs("div",{className:"flex-1 min-w-0",children:[e&&i.jsx("h3",{className:"text-lg font-medium text-gray-900 truncate",children:e}),t&&i.jsx("p",{className:"mt-1 text-sm text-gray-500",children:t})]}),n&&i.jsx("div",{className:"flex items-center space-x-2 ml-4",children:n})]}),c&&i.jsx("div",{className:K(ha[s],(e||t||n)&&s!=="none"&&"pt-0"),children:c}),r&&i.jsx("div",{className:K("border-t border-gray-200",s!=="none"&&"px-4 py-3"),children:r})]}),Gu=({title:e,subtitle:t,actions:n,className:r})=>i.jsxs("div",{className:K("flex items-start justify-between p-4 border-b border-gray-200",r),children:[i.jsxs("div",{className:"flex-1 min-w-0",children:[e&&i.jsx("h3",{className:"text-lg font-medium text-gray-900 truncate",children:e}),t&&i.jsx("p",{className:"mt-1 text-sm text-gray-500",children:t})]}),n&&i.jsx("div",{className:"flex items-center space-x-2 ml-4",children:n})]}),H=({children:e,className:t,padding:n="md"})=>i.jsx("div",{className:K(ha[n],t),children:e}),jy=({children:e,className:t,padding:n="md"})=>i.jsx("div",{className:K("border-t border-gray-200",ha[n],t),children:e}),ga={xs:{spinner:"w-3 h-3",text:"text-xs"},sm:{spinner:"w-4 h-4",text:"text-sm"},md:{spinner:"w-6 h-6",text:"text-base"},lg:{spinner:"w-8 h-8",text:"text-lg"},xl:{spinner:"w-12 h-12",text:"text-xl"}},Ny=({size:e,className:t})=>i.jsxs("svg",{className:K("animate-spin",ga[e].spinner,t),fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),_y=({size:e,className:t})=>{const n=e==="xs"?"w-1 h-1":e==="sm"?"w-1.5 h-1.5":e==="md"?"w-2 h-2":e==="lg"?"w-3 h-3":"w-4 h-4";return i.jsx("div",{className:K("flex space-x-1",t),children:[0,1,2].map(r=>i.jsx("div",{className:K("bg-current rounded-full animate-pulse",n),style:{animationDelay:`${r*.2}s`,animationDuration:"1s"}},r))})},Ey=({size:e,className:t})=>i.jsx("div",{className:K("bg-current rounded-full animate-pulse",ga[e].spinner,t)}),by=({size:e,className:t})=>{const n=e==="xs"?"h-2":e==="sm"?"h-3":e==="md"?"h-4":e==="lg"?"h-6":"h-8",r=e==="xs"?"w-0.5":e==="sm"?"w-1":"w-1.5";return i.jsx("div",{className:K("flex items-end space-x-1",t),children:[0,1,2,3].map(s=>i.jsx("div",{className:K("bg-current animate-pulse",r,n),style:{animationDelay:`${s*.15}s`,animationDuration:"1.2s"}},s))})},Zt=({size:e="md",variant:t="spinner",text:n,overlay:r=!1,fullScreen:s=!1,className:o,children:l,...a})=>{const u={spinner:Ny,dots:_y,pulse:Ey,bars:by}[t],c=i.jsxs("div",{className:K("flex flex-col items-center justify-center space-y-2",s&&"min-h-screen",o),...a,children:[i.jsx(u,{size:e,className:"text-blue-600"}),n&&i.jsx("p",{className:K("text-gray-600",ga[e].text),children:n}),l]});return r||s?i.jsx("div",{className:K("fixed inset-0 bg-white bg-opacity-75 backdrop-blur-sm z-50","flex items-center justify-center"),children:c}):c},Cy={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"},Zf=({isOpen:e,onClose:t,title:n,size:r="md",closeOnOverlayClick:s=!0,closeOnEscape:o=!0,showCloseButton:l=!0,footer:a,className:u,children:c,...f})=>{if(M.useEffect(()=>{if(!e||!o)return;const g=x=>{x.key==="Escape"&&t()};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[e,o,t]),M.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),!e)return null;const m=i.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",role:"dialog","aria-modal":"true","aria-labelledby":n?"modal-title":void 0,children:[i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:s?t:void 0}),i.jsxs("div",{className:K("relative bg-white rounded-lg shadow-xl w-full","transform transition-all duration-200",Cy[r],u),onClick:g=>g.stopPropagation(),...f,children:[(n||l)&&i.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[i.jsx("div",{className:"flex-1 min-w-0",children:n&&i.jsx("h2",{id:"modal-title",className:"text-lg font-semibold text-gray-900 truncate",children:n})}),l&&i.jsx("button",{type:"button",className:"ml-4 text-gray-400 hover:text-gray-600 transition-colors",onClick:t,"aria-label":"关闭模态框",children:i.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),i.jsx("div",{className:"p-4",children:c}),a&&i.jsx("div",{className:"flex items-center justify-end space-x-2 p-4 border-t border-gray-200",children:a})]})]});return pf.createPortal(m,document.body)},Py=({isOpen:e,onClose:t,onConfirm:n,title:r="确认操作",message:s="您确定要执行此操作吗？",confirmText:o="确认",cancelText:l="取消",variant:a="info"})=>{const u={danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500",warning:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500"};return i.jsx(Zf,{isOpen:e,onClose:t,title:r,size:"sm",footer:i.jsxs(i.Fragment,{children:[i.jsx("button",{type:"button",className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors",onClick:t,children:l}),i.jsx("button",{type:"button",className:K("px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 transition-colors",u[a]),onClick:()=>{n(),t()},children:o})]}),children:i.jsx("p",{className:"text-gray-600",children:s})})},Ry=({stats:e})=>{const t=[{title:"已导入页面",value:e.imported_count,icon:"📥",color:"blue"},{title:"已发布文章",value:e.published_count,icon:"✅",color:"green"},{title:"数据库总数",value:e.total_posts||0,icon:"📊",color:"purple"},{title:"同步错误",value:e.sync_errors||0,icon:"⚠️",color:e.sync_errors?"red":"gray"}];return i.jsxs("div",{className:"notion-wp-stats-grid",children:[t.map((n,r)=>i.jsxs("div",{className:`notion-wp-stat-card notion-wp-stat-card--${n.color}`,children:[i.jsx("div",{className:"notion-wp-stat-icon",children:n.icon}),i.jsxs("div",{className:"notion-wp-stat-content",children:[i.jsx("div",{className:"notion-wp-stat-value",children:n.value.toLocaleString()}),i.jsx("div",{className:"notion-wp-stat-title",children:n.title})]})]},r)),e.last_update&&i.jsxs("div",{className:"notion-wp-last-update",children:[i.jsx("span",{className:"notion-wp-last-update-label",children:"最后更新："}),i.jsx("span",{className:"notion-wp-last-update-time",children:new Date(e.last_update).toLocaleString("zh-CN")})]})]})},Ty=({disabled:e=!1})=>{const{startSync:t}=Wn(),[n,r]=M.useState(null),s=async l=>{r(l);try{l==="test"?await new Promise(a=>setTimeout(a,1e3)):await t({type:l,incremental:l==="smart",force_refresh:l==="full"})}catch(a){console.error(`${l} sync failed:`,a)}finally{r(null)}},o=[{id:"test",label:"🔗 测试连接",description:"验证Notion API连接状态",variant:"secondary",action:()=>s("test")},{id:"smart",label:"🚀 智能同步",description:"仅同步更新的内容",variant:"primary",action:()=>s("smart")},{id:"full",label:"📥 完整同步",description:"同步所有页面数据",variant:"warning",action:()=>s("full")}];return i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:o.map(l=>i.jsxs("div",{className:"space-y-2",children:[i.jsx(I,{variant:l.variant,size:"lg",fullWidth:!0,loading:n===l.id,disabled:e||n!==null,onClick:l.action,children:l.label}),i.jsx("p",{className:"text-sm text-gray-600 text-center",children:l.description})]},l.id))})},Ly=({progress:e,status:t,currentStep:n})=>{const r=o=>{switch(o){case"running":return"bg-blue-500";case"completed":return"bg-green-500";case"error":return"bg-red-500";case"paused":return"bg-yellow-500";default:return"bg-gray-500"}},s=o=>{switch(o){case"running":return"🔄";case"completed":return"✅";case"error":return"❌";case"paused":return"⏸️";default:return"⭕"}};return i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("span",{className:"text-lg",children:s(t)}),i.jsx("span",{className:"text-sm font-medium text-gray-700",children:n||"准备中..."})]}),i.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[Math.round(e),"%"]})]}),i.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:i.jsx("div",{className:K("h-full transition-all duration-300 ease-out rounded-full",r(t)),style:{width:`${e}%`},children:t==="running"&&i.jsx("div",{className:"h-full w-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"})})}),t==="running"&&i.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[i.jsx(Zt,{variant:"spinner",size:"xs"}),i.jsx("span",{children:"正在处理..."})]})]})},Ku=()=>{const{isRunning:e,progress:t,status:n,currentStep:r,stats:s}=Wn();return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"🔄 同步设置"}),i.jsx("p",{className:"text-sm text-gray-600",children:"配置Notion API并管理数据库同步"})]}),i.jsx(Ry,{stats:s||{imported_count:0,published_count:0,last_update:"",total_posts:0,sync_errors:0}}),e&&i.jsx($,{title:"同步进度",shadow:"md",children:i.jsx(H,{children:i.jsx(Ly,{progress:t,status:n,currentStep:r})})}),i.jsx($,{title:"同步操作",shadow:"md",children:i.jsx(H,{children:i.jsx(Ty,{disabled:e})})})]})},ul={api_key:"",database_id:"",post_type:"post",post_status:"publish",author_id:1,field_mapping:{title_field:"post_title",content_field:"post_content",excerpt_field:"post_excerpt",featured_image_field:"featured_image",category_field:"categories",tag_field:"tags",custom_fields:[]},custom_field_mapping:[],performance_config:{batch_size:10,request_delay:1e3,max_retries:3,timeout:3e4,enable_cache:!0,cache_duration:3600,max_execution_time:300,memory_limit:"256M",enable_async_processing:!0,enable_image_optimization:!0},language_settings:{default_language:"zh-CN",fallback_language:"en-US",auto_detect:!0,enable_multilingual:!1,language_mapping:{}},webhook_url:"",enable_webhook:!1,enable_auto_sync:!1,sync_interval:3600,enable_debug:!1,log_level:"info"},Oy={settings:null,isLoading:!1,isSaving:!1,isTesting:!1,validationResults:{},connectionStatus:"unknown",lastTestResult:null,lastSaved:null,hasUnsavedChanges:!1,error:null,fieldErrors:{}},bo=la()(ia((e,t)=>({...Oy,loadSettings:async()=>{e({isLoading:!0,error:null});try{const r=await Lt().getSettings();e({settings:{...ul,...r},isLoading:!1,hasUnsavedChanges:!1})}catch(n){const r=n instanceof Error?n.message:"加载设置失败";e({error:r,isLoading:!1,settings:ul}),console.error("❌ [设置Store] 加载设置失败:",n)}},saveSettings:async n=>{const r=t().settings;if(!r)return t().setError("没有可保存的设置数据"),!1;e({isSaving:!0,error:null});try{const s=n?{...r,...n}:r;return t().validateAllFields()?(await Lt().saveSettings(s),e({settings:s,isSaving:!1,hasUnsavedChanges:!1,lastSaved:new Date().toISOString()}),console.log("✅ [设置Store] 设置保存成功"),!0):(e({isSaving:!1}),!1)}catch(s){const o=s instanceof Error?s.message:"保存设置失败";return e({error:o,isSaving:!1}),console.error("❌ [设置Store] 保存设置失败:",s),!1}},updateSettings:n=>{e(r=>({settings:r.settings?{...r.settings,...n}:null,hasUnsavedChanges:!0}))},resetSettings:()=>{e({settings:ul,hasUnsavedChanges:!0,validationResults:{},fieldErrors:{}})},testConnection:async(n,r)=>{var a;const s=t().settings,o=n||(s==null?void 0:s.api_key),l=r||(s==null?void 0:s.database_id);if(!o||!l)return t().setError("请输入API密钥和数据库ID"),!1;e({isTesting:!0,connectionStatus:"testing",error:null});try{const c=await Lt().testConnection({api_key:o,database_id:l}),f=!!(c.success&&((a=c.data)!=null&&a.connected));return e({isTesting:!1,connectionStatus:f?"connected":"failed",lastTestResult:c,error:f?null:c.message||"连接测试失败"}),f}catch(u){const c=u instanceof Error?u.message:"连接测试失败";return e({isTesting:!1,connectionStatus:"failed",error:c}),console.error("❌ [设置Store] 连接测试失败:",u),!1}},updateFieldMapping:n=>{var s;const r=(s=t().settings)==null?void 0:s.field_mapping;r&&t().updateSettings({field_mapping:{...r,...n}})},addCustomFieldMapping:n=>{const r=t().settings;if(!r)return;const s=r.custom_field_mapping||[],o=s.findIndex(l=>l.notion_field===n.notion_field);o>=0?s[o]=n:s.push(n),t().updateSettings({custom_field_mapping:s})},removeCustomFieldMapping:n=>{const r=t().settings;if(!r)return;const o=(r.custom_field_mapping||[]).filter(l=>l.notion_field!==n);t().updateSettings({custom_field_mapping:o})},updatePerformanceConfig:n=>{var s;const r=(s=t().settings)==null?void 0:s.performance_config;r&&t().updateSettings({performance_config:{...r,...n}})},updateLanguageSettings:n=>{var s;const r=(s=t().settings)==null?void 0:s.language_settings;r&&t().updateSettings({language_settings:{...r,...n}})},validateField:(n,r)=>{let s=!0,o="";switch(n){case"api_key":s=typeof r=="string"&&r.length>0,o=s?"":"API密钥不能为空";break;case"database_id":s=typeof r=="string"&&r.length>0,o=s?"":"数据库ID不能为空";break;case"webhook_url":if(r&&typeof r=="string")try{new URL(r),s=!0}catch{s=!1,o="Webhook URL格式不正确"}break;default:s=!0}const l={isValid:s,is_valid:s,message:o};return e(a=>({validationResults:{...a.validationResults,[n]:l},fieldErrors:{...a.fieldErrors,[n]:s?"":o}})),l},validateAllFields:()=>{const n=t().settings;return n?[t().validateField("api_key",n.api_key),t().validateField("database_id",n.database_id),t().validateField("webhook_url",n.webhook_url)].every(s=>s.isValid):!1},clearValidation:()=>{e({validationResults:{},fieldErrors:{}})},setError:n=>{e({error:n})},setFieldError:(n,r)=>{e(s=>({fieldErrors:{...s.fieldErrors,[n]:r}}))},clearErrors:()=>{e({error:null,fieldErrors:{}})},markAsChanged:()=>{e({hasUnsavedChanges:!0})},markAsSaved:()=>{e({hasUnsavedChanges:!1,lastSaved:new Date().toISOString()})}}),{name:"notion-wp-settings-store",storage:xo(()=>localStorage),partialize:e=>({settings:e.settings,lastSaved:e.lastSaved,connectionStatus:e.connectionStatus})})),zy=()=>{const{settings:e,isLoading:t,isSaving:n,updateSettings:r,saveSettings:s,hasUnsavedChanges:o}=bo(),l=async()=>{await s()&&console.log("设置保存成功")};return t?i.jsxs("div",{className:"flex flex-col items-center justify-center py-12 space-y-4",children:[i.jsx(Zt,{variant:"spinner",size:"lg"}),i.jsx("p",{className:"text-gray-600",children:"正在加载设置..."})]}):i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"⚙️ 其他设置"}),i.jsx("p",{className:"text-sm text-gray-600",children:"配置插件的其他选项和高级功能"})]}),i.jsx($,{title:"基本设置",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"默认文章类型"}),i.jsxs("select",{className:"notion-wp-select",value:(e==null?void 0:e.post_type)||"post",onChange:a=>r({post_type:a.target.value}),children:[i.jsx("option",{value:"post",children:"文章 (Post)"}),i.jsx("option",{value:"page",children:"页面 (Page)"})]})]}),i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"默认文章状态"}),i.jsxs("select",{className:"notion-wp-select",value:(e==null?void 0:e.post_status)||"publish",onChange:a=>r({post_status:a.target.value}),children:[i.jsx("option",{value:"publish",children:"已发布"}),i.jsx("option",{value:"draft",children:"草稿"}),i.jsx("option",{value:"private",children:"私密"})]})]}),i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"作者ID"}),i.jsx("input",{type:"number",className:"notion-wp-input",value:(e==null?void 0:e.author_id)||1,onChange:a=>r({author_id:parseInt(a.target.value)||1}),min:"1"})]})]})}),i.jsx($,{title:"自动同步",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsxs("label",{className:"notion-wp-checkbox-label",children:[i.jsx("input",{type:"checkbox",className:"notion-wp-checkbox",checked:(e==null?void 0:e.enable_auto_sync)||!1,onChange:a=>r({enable_auto_sync:a.target.checked})}),"启用自动同步"]}),i.jsx("p",{className:"notion-wp-help-text",children:"启用后将定期自动同步Notion数据库内容"})]}),(e==null?void 0:e.enable_auto_sync)&&i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"同步间隔（秒）"}),i.jsx("input",{type:"number",className:"notion-wp-input",value:(e==null?void 0:e.sync_interval)||3600,onChange:a=>r({sync_interval:parseInt(a.target.value)||3600}),min:"300",max:"86400"}),i.jsx("p",{className:"notion-wp-help-text",children:"建议最小间隔为300秒（5分钟）"})]})]})}),i.jsx($,{title:"调试选项",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsxs("label",{className:"notion-wp-checkbox-label",children:[i.jsx("input",{type:"checkbox",className:"notion-wp-checkbox",checked:(e==null?void 0:e.enable_debug)||!1,onChange:a=>r({enable_debug:a.target.checked})}),"启用调试模式"]}),i.jsx("p",{className:"notion-wp-help-text",children:"启用后将记录详细的调试信息"})]}),i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"日志级别"}),i.jsxs("select",{className:"notion-wp-select",value:(e==null?void 0:e.log_level)||"info",onChange:a=>r({log_level:a.target.value}),children:[i.jsx("option",{value:"debug",children:"调试"}),i.jsx("option",{value:"info",children:"信息"}),i.jsx("option",{value:"warning",children:"警告"}),i.jsx("option",{value:"error",children:"错误"})]})]})]})}),o&&i.jsxs("div",{className:"flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[i.jsx("p",{className:"text-sm text-yellow-800",children:"您有未保存的更改"}),i.jsx(I,{variant:"primary",onClick:l,loading:n,disabled:n,children:n?"保存中...":"保存设置"})]})]})},Iy=()=>{const{settings:e,updateFieldMapping:t,addCustomFieldMapping:n,removeCustomFieldMapping:r,saveSettings:s,hasUnsavedChanges:o,isSaving:l}=bo(),[a,u]=M.useState({notion_field:"",wordpress_field:"",field_type:"text"}),c=(e==null?void 0:e.field_mapping)||{title_field:"",content_field:"",excerpt_field:"",featured_image_field:"",category_field:"",tag_field:"",custom_fields:[]},f=(e==null?void 0:e.custom_field_mapping)||[],m=(y,v)=>{t({[y]:v})},g=()=>{a.notion_field&&a.wordpress_field&&(n(a),u({notion_field:"",wordpress_field:"",field_type:"text"}))},x=async()=>{await s()&&console.log("字段映射保存成功")};return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"🔗 字段映射"}),i.jsx("p",{className:"text-sm text-gray-600",children:"配置Notion属性与WordPress字段的映射关系"})]}),i.jsx($,{title:"基础字段映射",subtitle:"配置Notion属性与WordPress标准字段的映射",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsx(q,{label:"标题字段",value:c.title_field||"",onChange:y=>m("title_field",y.target.value),placeholder:"例如：Title, 标题",helperText:"Notion中用作文章标题的属性名称"}),i.jsx(q,{label:"内容字段",value:c.content_field||"",onChange:y=>m("content_field",y.target.value),placeholder:"页面内容（自动获取）",disabled:!0,helperText:"页面内容自动从Notion页面获取"}),i.jsx(q,{label:"摘要字段",value:c.excerpt_field||"",onChange:y=>m("excerpt_field",y.target.value),placeholder:"例如：Summary, 摘要, Excerpt"}),i.jsx(q,{label:"特色图片字段",value:c.featured_image_field||"",onChange:y=>m("featured_image_field",y.target.value),placeholder:"例如：Featured Image, 特色图片"}),i.jsx(q,{label:"分类字段",value:c.category_field||"",onChange:y=>m("category_field",y.target.value),placeholder:"例如：Categories, 分类, Category"}),i.jsx(q,{label:"标签字段",value:c.tag_field||"",onChange:y=>m("tag_field",y.target.value),placeholder:"例如：Tags, 标签, Tag"})]})}),i.jsx($,{title:"自定义字段映射",subtitle:"将Notion属性映射到WordPress自定义字段",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[f.length>0&&i.jsx("div",{className:"notion-wp-custom-fields-list",children:f.map((y,v)=>i.jsxs("div",{className:"notion-wp-custom-field-item",children:[i.jsxs("div",{className:"notion-wp-custom-field-info",children:[i.jsxs("div",{className:"notion-wp-custom-field-name",children:[y.notion_field," → ",y.wordpress_field]}),i.jsxs("div",{className:"notion-wp-custom-field-type",children:["类型: ",y.field_type]})]}),i.jsx("button",{className:"notion-wp-button notion-wp-button--danger notion-wp-button--small",onClick:()=>r(y.notion_field),children:"删除"})]},v))}),i.jsx("div",{className:"notion-wp-add-custom-field",children:i.jsxs("div",{className:"notion-wp-form-row",children:[i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"Notion字段名"}),i.jsx("input",{type:"text",className:"notion-wp-input",value:a.notion_field,onChange:y=>u(v=>({...v,notion_field:y.target.value})),placeholder:"例如：Price, 价格"})]}),i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"WordPress字段名"}),i.jsx("input",{type:"text",className:"notion-wp-input",value:a.wordpress_field,onChange:y=>u(v=>({...v,wordpress_field:y.target.value})),placeholder:"例如：product_price"})]}),i.jsxs("div",{className:"notion-wp-form-group",children:[i.jsx("label",{className:"notion-wp-label",children:"字段类型"}),i.jsxs("select",{className:"notion-wp-select",value:a.field_type,onChange:y=>u(v=>({...v,field_type:y.target.value})),children:[i.jsx("option",{value:"text",children:"文本"}),i.jsx("option",{value:"number",children:"数字"}),i.jsx("option",{value:"date",children:"日期"}),i.jsx("option",{value:"boolean",children:"布尔值"}),i.jsx("option",{value:"url",children:"链接"}),i.jsx("option",{value:"email",children:"邮箱"})]})]}),i.jsx("div",{className:"notion-wp-form-group",children:i.jsx("button",{className:"notion-wp-button notion-wp-button--primary",onClick:g,disabled:!a.notion_field||!a.wordpress_field,children:"添加"})})]})})]})}),o&&i.jsxs("div",{className:"flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[i.jsx("p",{className:"text-sm text-yellow-800",children:"您有未保存的更改"}),i.jsx(I,{variant:"primary",onClick:x,loading:l,disabled:l,children:l?"保存中...":"保存映射"})]})]})},Ay=()=>{const{settings:e,updateSettings:t,saveSettings:n,hasUnsavedChanges:r,isSaving:s}=bo(),o=(e==null?void 0:e.performance_config)||{enable_cache:!0,cache_duration:3600,batch_size:20,max_execution_time:300,memory_limit:"256M",enable_async_processing:!0,enable_image_optimization:!0,max_retries:3,timeout:3e4,request_delay:100},l=(u,c)=>{t({performance_config:{...o,[u]:c}})},a=async()=>{await n()&&console.log("性能配置保存成功")};return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"⚡ 性能配置"}),i.jsx("p",{className:"text-sm text-gray-600",children:"优化同步性能和资源使用"})]}),i.jsx($,{title:"API 性能配置",subtitle:"调整API请求的性能参数",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsx(q,{label:"缓存持续时间 (秒)",type:"number",value:o.cache_duration.toString(),onChange:u=>l("cache_duration",parseInt(u.target.value)||3600),helperText:"缓存数据的有效时间",min:"300",max:"86400"}),i.jsx(q,{label:"批处理大小",type:"number",value:o.batch_size.toString(),onChange:u=>l("batch_size",parseInt(u.target.value)||20),helperText:"批量处理的文章数量",min:"5",max:"100"}),i.jsx(q,{label:"最大执行时间 (秒)",type:"number",value:o.max_execution_time.toString(),onChange:u=>l("max_execution_time",parseInt(u.target.value)||300),helperText:"脚本最大执行时间",min:"60",max:"3600"}),i.jsx(q,{label:"内存限制",type:"text",value:o.memory_limit,onChange:u=>l("memory_limit",u.target.value),helperText:"PHP内存限制 (如: 256M, 512M)",placeholder:"256M"}),i.jsx(q,{label:"最大重试次数",type:"number",value:o.max_retries.toString(),onChange:u=>l("max_retries",parseInt(u.target.value)||3),helperText:"API请求失败时的重试次数",min:"0",max:"10"}),i.jsx(q,{label:"请求超时时间 (毫秒)",type:"number",value:o.timeout.toString(),onChange:u=>l("timeout",parseInt(u.target.value)||3e4),helperText:"API请求的超时时间",min:"5000",max:"120000"})]}),i.jsxs("div",{className:"pt-4 border-t space-y-4",children:[i.jsxs("label",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"checkbox",checked:o.enable_cache,onChange:u=>l("enable_cache",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),i.jsx("span",{className:"text-sm font-medium text-gray-700",children:"启用缓存"})]}),i.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"启用缓存可以显著提高同步性能"}),i.jsxs("label",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"checkbox",checked:o.enable_async_processing,onChange:u=>l("enable_async_processing",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),i.jsx("span",{className:"text-sm font-medium text-gray-700",children:"启用异步处理"})]}),i.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"异步处理可以提高大批量同步的效率"}),i.jsxs("label",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"checkbox",checked:o.enable_image_optimization,onChange:u=>l("enable_image_optimization",u.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),i.jsx("span",{className:"text-sm font-medium text-gray-700",children:"启用图片优化"})]}),i.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"自动优化和压缩同步的图片文件"})]})]})}),i.jsx($,{title:"缓存配置",subtitle:"配置缓存策略以提高性能",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.jsx(q,{label:"请求延迟 (毫秒)",type:"number",value:o.request_delay.toString(),onChange:u=>l("request_delay",parseInt(u.target.value)||100),helperText:"请求之间的延迟时间",min:"0",max:"5000"})}),i.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[i.jsx("h4",{className:"text-sm font-medium text-blue-800 mb-2",children:"💡 性能优化建议"}),i.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[i.jsx("li",{children:"• 增加API页面大小可以减少请求次数，但会增加内存使用"}),i.jsx("li",{children:"• 适当的并发请求数可以提高同步速度，但过多可能导致API限制"}),i.jsx("li",{children:"• 启用性能模式适合服务器性能较好的环境"}),i.jsx("li",{children:"• 增加请求延迟可以避免触发API速率限制"})]})]})]})}),r&&i.jsxs("div",{className:"flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[i.jsx("p",{className:"text-sm text-yellow-800",children:"您有未保存的更改"}),i.jsx(I,{variant:"primary",onClick:a,loading:s,disabled:s,children:s?"保存中...":"保存配置"})]})]})},My={activeTab:"sync",tabHistory:["sync"],notifications:[],maxNotifications:5,modals:[],globalLoading:!1,loadingMessage:"",sidebarCollapsed:!1,theme:"auto",isPageVisible:!0,isMobile:!1,isTablet:!1,debugMode:!1},Ju=()=>`${Date.now()}_${Math.random().toString(36).substring(2,9)}`,ya=la()(ia((e,t)=>({...My,setActiveTab:n=>{e(r=>{const s=[...r.tabHistory],o=s.indexOf(n);return o>-1&&s.splice(o,1),s.push(n),s.length>10&&s.shift(),{activeTab:n,tabHistory:s}})},goBack:()=>{const{tabHistory:n,activeTab:r}=t(),s=n.indexOf(r);s>0&&t().setActiveTab(n[s-1])},goForward:()=>{const{tabHistory:n,activeTab:r}=t(),s=n.indexOf(r);s<n.length-1&&t().setActiveTab(n[s+1])},showNotification:n=>{const r=Ju(),s={...n,id:r,timestamp:Date.now(),duration:n.duration??5e3};return e(o=>{let l=[...o.notifications,s];return l.length>o.maxNotifications&&(l=l.slice(-o.maxNotifications)),{notifications:l}}),s.duration&&s.duration>0&&setTimeout(()=>{t().hideNotification(r)},s.duration),r},hideNotification:n=>{e(r=>({notifications:r.notifications.filter(s=>s.id!==n)}))},clearNotifications:()=>{e({notifications:[]})},showSuccess:(n,r,s=5e3)=>t().showNotification({type:"success",title:n,message:r,duration:s}),showError:(n,r,s=0)=>t().showNotification({type:"error",title:n,message:r,duration:s}),showWarning:(n,r,s=8e3)=>t().showNotification({type:"warning",title:n,message:r,duration:s}),showInfo:(n,r,s=5e3)=>t().showNotification({type:"info",title:n,message:r,duration:s}),showModal:n=>{const r=Ju(),s={...n,id:r,isOpen:!0};return e(o=>({modals:[...o.modals,s]})),r},hideModal:n=>{e(r=>({modals:r.modals.filter(s=>s.id!==n)}))},hideAllModals:()=>{e({modals:[]})},showConfirm:(n,r,s,o)=>t().showModal({type:"confirm",title:n,content:r,onConfirm:s,onCancel:o,confirmText:"确认",cancelText:"取消"}),showAlert:(n,r,s)=>t().showModal({type:"alert",title:n,content:r,onConfirm:s,confirmText:"确定"}),setGlobalLoading:(n,r="")=>{e({globalLoading:n,loadingMessage:r})},toggleSidebar:()=>{e(n=>({sidebarCollapsed:!n.sidebarCollapsed}))},setSidebarCollapsed:n=>{e({sidebarCollapsed:n})},setTheme:n=>{e({theme:n});const r=document.documentElement;n==="dark"?r.classList.add("dark"):n==="light"?r.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?r.classList.add("dark"):r.classList.remove("dark")},setPageVisible:n=>{e({isPageVisible:n})},setResponsiveState:(n,r)=>{e({isMobile:n,isTablet:r})},toggleDebugMode:()=>{e(n=>({debugMode:!n.debugMode}))},setDebugMode:n=>{e({debugMode:n})}}),{name:"notion-wp-ui-store",storage:xo(()=>localStorage),partialize:e=>({activeTab:e.activeTab,sidebarCollapsed:e.sidebarCollapsed,theme:e.theme,debugMode:e.debugMode})})),Dy=()=>{const{sseConnected:e,startTime:t,progress:n}=Wn(),{showSuccess:r,showError:s}=ya(),[o,l]=M.useState(null),[a,u]=M.useState(null),[c,f]=M.useState(!1),[m,g]=M.useState(!1),x={php_version:"8.1.0",memory_usage:"45MB",memory_limit:"256MB",execution_time:"2.3s",wordpress_version:"6.4.0",plugin_version:"1.0.0",last_sync:t?new Date(t).toLocaleString():"从未同步",total_synced:156,sync_errors:3},y={avg_sync_time:1.8,success_rate:98.1,error_rate:1.9,memory_peak:"78MB",cpu_usage:23,active_connections:2},v=async()=>{f(!0);try{await new Promise(d=>setTimeout(d,1e3)),l(x),u(y),r("系统信息已更新","系统信息已成功刷新")}catch{s("获取系统信息失败","请检查网络连接后重试")}finally{f(!1)}},w=async()=>{f(!0);try{await new Promise(d=>setTimeout(d,800)),r("缓存已清除","系统缓存已成功清除")}catch{s("清除缓存失败","请稍后重试")}finally{f(!1)}},p=async()=>{f(!0);try{await new Promise(d=>setTimeout(d,2e3)),r("数据库优化完成","数据库已成功优化")}catch{s("数据库优化失败","请稍后重试")}finally{f(!1)}};return M.useEffect(()=>{v()},[]),M.useEffect(()=>{let d;return m&&(d=window.setInterval(v,3e4)),()=>{d&&clearInterval(d)}},[m]),i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"📊 性能监控"}),i.jsx("p",{className:"text-sm text-gray-600",children:"实时监控系统性能和同步状态"})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[i.jsx($,{shadow:"sm",className:"border-l-4 border-l-green-500",children:i.jsx(H,{className:"p-4",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("p",{className:"text-sm text-gray-600",children:"连接状态"}),i.jsx("p",{className:"text-lg font-semibold text-green-600",children:e?"已连接":"未连接"})]}),i.jsx("div",{className:"text-2xl",children:"🔗"})]})})}),i.jsx($,{shadow:"sm",className:"border-l-4 border-l-blue-500",children:i.jsx(H,{className:"p-4",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("p",{className:"text-sm text-gray-600",children:"同步进度"}),i.jsxs("p",{className:"text-lg font-semibold text-blue-600",children:[n||0,"%"]})]}),i.jsx("div",{className:"text-2xl",children:"⚡"})]})})}),i.jsx($,{shadow:"sm",className:"border-l-4 border-l-purple-500",children:i.jsx(H,{className:"p-4",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("p",{className:"text-sm text-gray-600",children:"总同步数"}),i.jsx("p",{className:"text-lg font-semibold text-purple-600",children:(o==null?void 0:o.total_synced)||0})]}),i.jsx("div",{className:"text-2xl",children:"📈"})]})})})]}),i.jsx($,{title:"系统信息",subtitle:"查看当前系统状态和配置信息",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[c&&!o?i.jsx(Zt,{text:"正在加载系统信息..."}):o?i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"PHP 版本"}),i.jsx("p",{className:"text-sm text-gray-600",children:o.php_version})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"内存使用"}),i.jsxs("p",{className:"text-sm text-gray-600",children:[o.memory_usage," / ",o.memory_limit]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"执行时间"}),i.jsx("p",{className:"text-sm text-gray-600",children:o.execution_time})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"WordPress 版本"}),i.jsx("p",{className:"text-sm text-gray-600",children:o.wordpress_version})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"插件版本"}),i.jsx("p",{className:"text-sm text-gray-600",children:o.plugin_version})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"font-medium text-gray-700",children:"最后同步"}),i.jsx("p",{className:"text-sm text-gray-600",children:o.last_sync})]})]}):i.jsx("p",{className:"text-gray-500",children:"无法获取系统信息"}),i.jsxs("div",{className:"flex flex-wrap gap-2 pt-4 border-t",children:[i.jsx(I,{variant:"primary",onClick:v,loading:c,disabled:c,children:"刷新信息"}),i.jsxs("label",{className:"flex items-center space-x-2",children:[i.jsx("input",{type:"checkbox",checked:m,onChange:d=>g(d.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),i.jsx("span",{className:"text-sm text-gray-700",children:"自动刷新 (30秒)"})]})]})]})}),i.jsx($,{title:"性能指标",subtitle:"查看详细的性能统计数据",shadow:"md",children:i.jsx(H,{className:"space-y-4",children:a?i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[i.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[i.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[a.avg_sync_time,"s"]}),i.jsx("div",{className:"text-sm text-blue-700",children:"平均同步时间"})]}),i.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[i.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[a.success_rate,"%"]}),i.jsx("div",{className:"text-sm text-green-700",children:"成功率"})]}),i.jsxs("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[i.jsxs("div",{className:"text-2xl font-bold text-red-600",children:[a.error_rate,"%"]}),i.jsx("div",{className:"text-sm text-red-700",children:"错误率"})]}),i.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[i.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a.memory_peak}),i.jsx("div",{className:"text-sm text-purple-700",children:"内存峰值"})]}),i.jsxs("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[i.jsxs("div",{className:"text-2xl font-bold text-yellow-600",children:[a.cpu_usage,"%"]}),i.jsx("div",{className:"text-sm text-yellow-700",children:"CPU 使用率"})]}),i.jsxs("div",{className:"text-center p-4 bg-indigo-50 rounded-lg",children:[i.jsx("div",{className:"text-2xl font-bold text-indigo-600",children:a.active_connections}),i.jsx("div",{className:"text-sm text-indigo-700",children:"活跃连接"})]})]}):i.jsx("p",{className:"text-gray-500",children:"无法获取性能指标"})})}),i.jsx($,{title:"维护工具",subtitle:"系统维护和优化工具",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsx(I,{variant:"secondary",onClick:w,loading:c,disabled:c,className:"w-full",children:"清除缓存"}),i.jsx(I,{variant:"secondary",onClick:p,loading:c,disabled:c,className:"w-full",children:"优化数据库"})]}),i.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[i.jsx("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:"⚠️ 维护提示"}),i.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[i.jsx("li",{children:"• 清除缓存会临时影响性能，建议在低峰期执行"}),i.jsx("li",{children:"• 数据库优化可能需要较长时间，请耐心等待"}),i.jsx("li",{children:"• 建议定期执行维护操作以保持最佳性能"})]})]})]})})]})},Fy=()=>{const{showSuccess:e,showError:t}=ya(),[n,r]=M.useState([]),[s,o]=M.useState([]),[l,a]=M.useState(!1),[u,c]=M.useState({level:"all",search:"",dateFrom:"",dateTo:""}),f=[{id:"1",timestamp:"2024-01-15 10:30:25",level:"info",message:"同步任务开始执行",context:{task_id:"sync_001",pages:25}},{id:"2",timestamp:"2024-01-15 10:30:45",level:"info",message:'成功同步页面: "产品介绍"',context:{page_id:"abc123",title:"产品介绍"}},{id:"3",timestamp:"2024-01-15 10:31:02",level:"warning",message:"API请求速率接近限制",context:{rate_limit:"80%",remaining:20}},{id:"4",timestamp:"2024-01-15 10:31:15",level:"error",message:"同步失败: 网络连接超时",context:{error_code:"TIMEOUT",retry_count:3}},{id:"5",timestamp:"2024-01-15 10:31:30",level:"info",message:"同步任务完成",context:{task_id:"sync_001",success:23,failed:2}},{id:"6",timestamp:"2024-01-15 10:32:00",level:"debug",message:"缓存已更新",context:{cache_key:"notion_pages",size:"2.5MB"}}],m=async()=>{a(!0);try{await new Promise(w=>setTimeout(w,800)),r(f),e("日志已加载","日志数据已成功刷新")}catch{t("加载日志失败","请检查网络连接后重试")}finally{a(!1)}},g=async()=>{if(window.confirm("确定要清除所有日志吗？此操作不可撤销。")){a(!0);try{await new Promise(w=>setTimeout(w,500)),r([]),o([]),e("日志已清除","所有日志已成功清除")}catch{t("清除日志失败","请稍后重试")}finally{a(!1)}}},x=()=>{const w=s.map(k=>`[${k.timestamp}] ${k.level.toUpperCase()}: ${k.message}${k.context?" | Context: "+JSON.stringify(k.context):""}`).join(`
`),p=new Blob([w],{type:"text/plain"}),d=URL.createObjectURL(p),h=document.createElement("a");h.href=d,h.download=`notion-wp-logs-${new Date().toISOString().split("T")[0]}.txt`,document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(d),e("日志已下载","日志文件已保存到下载目录")},y=w=>{switch(w){case"error":return"text-red-600 bg-red-50 border-red-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"info":return"text-blue-600 bg-blue-50 border-blue-200";case"debug":return"text-gray-600 bg-gray-50 border-gray-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},v=w=>{switch(w){case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";case"debug":return"🔍";default:return"📝"}};return M.useEffect(()=>{let w=n;if(u.level!=="all"&&(w=w.filter(p=>p.level===u.level)),u.search){const p=u.search.toLowerCase();w=w.filter(d=>d.message.toLowerCase().includes(p)||d.context&&JSON.stringify(d.context).toLowerCase().includes(p))}u.dateFrom&&(w=w.filter(p=>p.timestamp>=u.dateFrom)),u.dateTo&&(w=w.filter(p=>p.timestamp<=u.dateTo)),o(w)},[n,u]),M.useEffect(()=>{m()},[]),i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"📋 日志查看器"}),i.jsx("p",{className:"text-sm text-gray-600",children:"查看和管理系统日志"})]}),i.jsx($,{title:"日志过滤",subtitle:"筛选和搜索日志条目",shadow:"md",children:i.jsxs(H,{className:"space-y-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"日志级别"}),i.jsxs("select",{value:u.level,onChange:w=>c(p=>({...p,level:w.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"all",children:"全部"}),i.jsx("option",{value:"error",children:"错误"}),i.jsx("option",{value:"warning",children:"警告"}),i.jsx("option",{value:"info",children:"信息"}),i.jsx("option",{value:"debug",children:"调试"})]})]}),i.jsx(q,{label:"搜索关键词",value:u.search,onChange:w=>c(p=>({...p,search:w.target.value})),placeholder:"搜索日志内容..."}),i.jsx(q,{label:"开始日期",type:"date",value:u.dateFrom,onChange:w=>c(p=>({...p,dateFrom:w.target.value}))}),i.jsx(q,{label:"结束日期",type:"date",value:u.dateTo,onChange:w=>c(p=>({...p,dateTo:w.target.value}))})]}),i.jsxs("div",{className:"flex flex-wrap gap-2 pt-4 border-t",children:[i.jsx(I,{variant:"primary",onClick:m,loading:l,disabled:l,children:"刷新日志"}),i.jsx(I,{variant:"secondary",onClick:x,disabled:s.length===0,children:"下载日志"}),i.jsx(I,{variant:"warning",onClick:g,loading:l,disabled:l||n.length===0,children:"清除日志"})]})]})}),i.jsx($,{title:`日志条目 (${s.length})`,subtitle:"按时间倒序显示",shadow:"md",children:i.jsx(H,{children:l?i.jsxs("div",{className:"text-center py-8",children:[i.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),i.jsx("p",{className:"mt-2 text-gray-600",children:"正在加载日志..."})]}):s.length>0?i.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:s.map(w=>i.jsx("div",{className:`p-3 rounded-lg border ${y(w.level)}`,children:i.jsx("div",{className:"flex items-start justify-between",children:i.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[i.jsx("span",{className:"text-lg",children:v(w.level)}),i.jsxs("div",{className:"flex-1",children:[i.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[i.jsx("span",{className:"text-xs font-medium uppercase tracking-wide",children:w.level}),i.jsx("span",{className:"text-xs text-gray-500",children:w.timestamp})]}),i.jsx("p",{className:"text-sm font-medium",children:w.message}),w.context&&i.jsxs("details",{className:"mt-2",children:[i.jsx("summary",{className:"text-xs text-gray-600 cursor-pointer hover:text-gray-800",children:"查看详细信息"}),i.jsx("pre",{className:"mt-1 text-xs bg-white bg-opacity-50 p-2 rounded border overflow-x-auto",children:JSON.stringify(w.context,null,2)})]})]})]})})},w.id))}):i.jsx("div",{className:"text-center py-8 text-gray-500",children:n.length===0?"暂无日志记录":"没有符合条件的日志"})})})]})},Uy=()=>{const{showSuccess:e,showError:t}=ya(),{sseConnected:n}=Wn(),[r,s]=M.useState(null),[o,l]=M.useState([]),[a,u]=M.useState([]),[c,f]=M.useState(!1),[m,g]=M.useState(""),[x,y]=M.useState("all"),v={php_version:"8.1.0",wp_version:"6.4.0",memory_limit:"256M",max_execution_time:"300",plugin_version:"2.0.0-beta.1",current_time:new Date().toISOString(),options_exist:"yes",ajax_url:"/wp-admin/admin-ajax.php"},w=[{id:"1",timestamp:"2024-01-15 10:30:25",level:"error",message:"API连接失败: 无法连接到Notion API",context:"Notion API Client",file:"NotionApiClient.php",line:156},{id:"2",timestamp:"2024-01-15 10:25:12",level:"warning",message:"同步超时: 页面同步耗时过长",context:"Sync Manager",file:"SyncManager.php",line:89},{id:"3",timestamp:"2024-01-15 10:20:45",level:"info",message:"同步完成: 成功同步5个页面",context:"Sync Process",file:"SyncProcess.php",line:234},{id:"4",timestamp:"2024-01-15 10:15:30",level:"debug",message:"缓存清理: 清除了过期的API响应缓存",context:"Cache Manager",file:"CacheManager.php",line:67},{id:"5",timestamp:"2024-01-15 10:10:18",level:"error",message:"数据库错误: 无法更新文章元数据",context:"Database Handler",file:"DatabaseHandler.php",line:123}],p=[{name:"PHP版本检查",status:"pass",message:"PHP 8.1.0 - 版本符合要求",details:"推荐PHP 8.0+，当前版本满足要求"},{name:"WordPress版本检查",status:"pass",message:"WordPress 6.4.0 - 版本符合要求",details:"推荐WordPress 6.0+，当前版本满足要求"},{name:"内存限制检查",status:"warning",message:"内存限制 256M - 建议增加",details:"推荐512M以上，当前可能在大量数据同步时不足"},{name:"cURL扩展检查",status:"pass",message:"cURL扩展已启用",details:"API通信必需的扩展已正确安装"},{name:"SSL证书检查",status:"pass",message:"SSL证书有效",details:"HTTPS连接正常，可以安全访问Notion API"},{name:"API连接测试",status:"fail",message:"API连接失败",details:"无法连接到Notion API，请检查网络和API密钥"}];M.useEffect(()=>{s(v),l(w),u(p)},[]);const d=async()=>{f(!0);try{await new Promise(C=>setTimeout(C,1e3)),s(v),e("系统信息","系统信息已更新")}catch{t("系统信息","获取系统信息失败")}finally{f(!1)}},h=async()=>{f(!0);try{await new Promise(C=>setTimeout(C,2e3)),u(p),e("系统诊断","系统诊断完成")}catch{t("系统诊断","系统诊断失败")}finally{f(!1)}},k=async()=>{f(!0);try{await new Promise(C=>setTimeout(C,500)),l([]),e("日志管理","错误日志已清除")}catch{t("日志管理","清除日志失败")}finally{f(!1)}},E=()=>{const C=b.map(ze=>`[${ze.timestamp}] ${ze.level.toUpperCase()}: ${ze.message} (${ze.context})`).join(`
`),O=new Blob([C],{type:"text/plain"}),ue=URL.createObjectURL(O),ve=document.createElement("a");ve.href=ue,ve.download=`debug-logs-${new Date().toISOString().split("T")[0]}.txt`,document.body.appendChild(ve),ve.click(),document.body.removeChild(ve),URL.revokeObjectURL(ue),e("日志下载","日志文件已下载")},b=o.filter(C=>{const O=m===""||C.message.toLowerCase().includes(m.toLowerCase())||C.context.toLowerCase().includes(m.toLowerCase()),ue=x==="all"||C.level===x;return O&&ue}),P=C=>{switch(C){case"pass":return"✅";case"fail":return"❌";case"warning":return"⚠️";default:return"❓"}},N=C=>{switch(C){case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";case"debug":return"🔍";default:return"📝"}};return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800 flex items-center gap-2",children:"🐞 调试工具"}),i.jsx("p",{className:"text-sm text-gray-600",children:"系统诊断、错误日志分析和调试实用工具"})]}),i.jsx($,{children:i.jsxs(H,{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-800",children:"📊 系统信息"}),i.jsx(I,{onClick:d,disabled:c,className:"bg-blue-600 hover:bg-blue-700",children:c?"获取中...":"刷新信息"})]}),r&&i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"PHP版本"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.php_version})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"WordPress版本"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.wp_version})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"内存限制"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.memory_limit})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"执行时间限制"}),i.jsxs("div",{className:"text-lg font-semibold text-gray-800",children:[r.max_execution_time,"s"]})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"插件版本"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.plugin_version})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"配置状态"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:r.options_exist==="yes"?"✅ 已配置":"❌ 未配置"})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"连接状态"}),i.jsx("div",{className:"text-lg font-semibold text-gray-800",children:n?"✅ 已连接":"❌ 未连接"})]}),i.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[i.jsx("div",{className:"text-sm font-medium text-gray-600",children:"当前时间"}),i.jsx("div",{className:"text-sm font-semibold text-gray-800",children:new Date(r.current_time).toLocaleString()})]})]})]})}),i.jsx($,{children:i.jsxs(H,{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-800",children:"🔍 系统诊断"}),i.jsx(I,{onClick:h,disabled:c,className:"bg-green-600 hover:bg-green-700",children:c?"诊断中...":"运行诊断"})]}),i.jsx("div",{className:"space-y-3",children:a.map((C,O)=>i.jsxs("div",{className:`p-4 rounded-lg border-l-4 ${C.status==="pass"?"bg-green-50 border-green-400":C.status==="warning"?"bg-yellow-50 border-yellow-400":"bg-red-50 border-red-400"}`,children:[i.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[i.jsx("span",{className:"text-lg",children:P(C.status)}),i.jsx("span",{className:"font-medium text-gray-800",children:C.name})]}),i.jsx("div",{className:"text-sm text-gray-600 mb-1",children:C.message}),C.details&&i.jsx("div",{className:"text-xs text-gray-500",children:C.details})]},O))})]})}),i.jsx($,{children:i.jsxs(H,{children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-gray-800",children:"📋 错误日志分析"}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx(I,{onClick:E,disabled:o.length===0,className:"bg-blue-600 hover:bg-blue-700",children:"下载日志"}),i.jsx(I,{onClick:k,disabled:c||o.length===0,className:"bg-red-600 hover:bg-red-700",children:c?"清除中...":"清除日志"})]})]}),i.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 mb-4",children:[i.jsx("div",{className:"flex-1",children:i.jsx(q,{type:"text",placeholder:"搜索日志内容...",value:m,onChange:C=>g(C.target.value),className:"w-full"})}),i.jsx("div",{className:"sm:w-48",children:i.jsxs("select",{value:x,onChange:C=>y(C.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[i.jsx("option",{value:"all",children:"所有级别"}),i.jsx("option",{value:"error",children:"错误"}),i.jsx("option",{value:"warning",children:"警告"}),i.jsx("option",{value:"info",children:"信息"}),i.jsx("option",{value:"debug",children:"调试"})]})})]}),i.jsxs("div",{className:"mb-4 text-sm text-gray-600",children:["显示 ",b.length," / ",o.length," 条日志"]}),i.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:b.length===0?i.jsx("div",{className:"text-center py-8 text-gray-500",children:o.length===0?"暂无日志记录":"没有匹配的日志"}):b.map(C=>i.jsx("div",{className:`p-3 rounded-lg border-l-4 ${C.level==="error"?"bg-red-50 border-red-400":C.level==="warning"?"bg-yellow-50 border-yellow-400":C.level==="info"?"bg-blue-50 border-blue-400":"bg-gray-50 border-gray-400"}`,children:i.jsxs("div",{className:"flex items-start gap-2",children:[i.jsx("span",{className:"text-lg flex-shrink-0 mt-0.5",children:N(C.level)}),i.jsxs("div",{className:"flex-1 min-w-0",children:[i.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[i.jsx("span",{className:"text-xs font-medium text-gray-500 uppercase",children:C.level}),i.jsx("span",{className:"text-xs text-gray-500",children:C.timestamp}),i.jsxs("span",{className:"text-xs text-gray-500",children:["(",C.context,")"]})]}),i.jsx("div",{className:"text-sm text-gray-800 mb-1",children:C.message}),C.file&&C.line&&i.jsxs("div",{className:"text-xs text-gray-500",children:[C.file,":",C.line]})]})]})},C.id))})]})}),i.jsx($,{children:i.jsxs(H,{children:[i.jsx("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"🛠️ 调试实用工具"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"🔗 API连接测试"}),i.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"测试与Notion API的连接状态"}),i.jsx(I,{onClick:()=>e("API测试","API测试功能开发中"),className:"w-full bg-blue-600 hover:bg-blue-700",children:"测试API连接"})]}),i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"🗄️ 缓存管理"}),i.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"清除和管理插件缓存"}),i.jsx(I,{onClick:()=>e("缓存管理","缓存已清除"),className:"w-full bg-green-600 hover:bg-green-700",children:"清除缓存"})]}),i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"🗃️ 数据库检查"}),i.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"检查数据库表结构和数据完整性"}),i.jsx(I,{onClick:()=>e("数据库检查","数据库检查完成"),className:"w-full bg-purple-600 hover:bg-purple-700",children:"检查数据库"})]}),i.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"⚙️ 配置验证"}),i.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"验证插件配置的正确性"}),i.jsx(I,{onClick:()=>e("配置验证","配置验证完成"),className:"w-full bg-orange-600 hover:bg-orange-700",children:"验证配置"})]})]}),i.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[i.jsx("h4",{className:"font-medium text-blue-800 mb-2 flex items-center gap-2",children:"💡 调试提示"}),i.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[i.jsx("li",{children:"• 遇到问题时，首先查看错误日志获取详细信息"}),i.jsx("li",{children:"• 运行系统诊断可以快速发现常见配置问题"}),i.jsx("li",{children:"• 定期清除缓存有助于解决数据不一致问题"}),i.jsx("li",{children:"• 下载日志文件可以方便地与技术支持分享问题信息"})]})]})]})})]})},By=()=>i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"📖 使用帮助"}),i.jsx("p",{className:"text-sm text-gray-600",children:"插件使用指南和常见问题解答"})]}),i.jsx("div",{className:"notion-wp-card",children:i.jsx("div",{className:"notion-wp-card-body",children:i.jsx("p",{className:"text-gray-600",children:"帮助内容组件正在开发中..."})})})]}),$y=()=>i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"👨‍💻 关于作者"}),i.jsx("p",{className:"text-sm text-gray-600",children:"插件开发者信息和致谢"})]}),i.jsx("div",{className:"notion-wp-card",children:i.jsx("div",{className:"notion-wp-card-body",children:i.jsx("p",{className:"text-gray-600",children:"关于作者组件正在开发中..."})})})]}),Hy=()=>{const[e,t]=M.useState(!1),[n,r]=M.useState(!1),[s,o]=M.useState(""),[l,a]=M.useState(!1),u=()=>{a(!0),setTimeout(()=>a(!1),2e3)};return i.jsxs("div",{className:"space-y-8 p-6",children:[i.jsxs("div",{className:"notion-wp-header-section",children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"🎨 基础组件库展示"}),i.jsx("p",{className:"text-gray-600",children:"展示所有可用的基础UI组件和它们的使用方式"})]}),i.jsx($,{title:"按钮组件 (Button)",subtitle:"支持多种变体、尺寸和状态",children:i.jsx(H,{children:i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"变体样式"}),i.jsxs("div",{className:"flex flex-wrap gap-2",children:[i.jsx(I,{variant:"primary",children:"主要按钮"}),i.jsx(I,{variant:"secondary",children:"次要按钮"}),i.jsx(I,{variant:"success",children:"成功按钮"}),i.jsx(I,{variant:"warning",children:"警告按钮"}),i.jsx(I,{variant:"danger",children:"危险按钮"}),i.jsx(I,{variant:"ghost",children:"幽灵按钮"}),i.jsx(I,{variant:"outline",children:"轮廓按钮"}),i.jsx(I,{variant:"link",children:"链接按钮"})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"尺寸大小"}),i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(I,{size:"xs",children:"超小"}),i.jsx(I,{size:"sm",children:"小"}),i.jsx(I,{size:"md",children:"中等"}),i.jsx(I,{size:"lg",children:"大"}),i.jsx(I,{size:"xl",children:"超大"})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"状态"}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx(I,{loading:l,onClick:u,children:l?"加载中...":"点击测试加载"}),i.jsx(I,{disabled:!0,children:"禁用按钮"}),i.jsx(I,{fullWidth:!0,children:"全宽按钮"})]})]})]})})}),i.jsx($,{title:"输入框组件 (Input)",subtitle:"支持多种类型、状态和验证",children:i.jsx(H,{children:i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsx(q,{label:"基础输入框",placeholder:"请输入内容",value:s,onChange:c=>o(c.target.value)}),i.jsx(q,{label:"带图标的输入框",placeholder:"搜索...",leftIcon:i.jsx("span",{children:"🔍"})}),i.jsx(q,{label:"成功状态",status:"success",helperText:"输入正确",defaultValue:"正确的输入"}),i.jsx(q,{label:"错误状态",status:"error",errorText:"请输入有效的邮箱地址",defaultValue:"invalid-email"}),i.jsx(q,{label:"加载状态",loading:!0,placeholder:"验证中..."}),i.jsx(q,{label:"禁用状态",disabled:!0,placeholder:"不可编辑"})]})})}),i.jsx($,{title:"卡片组件 (Card)",subtitle:"灵活的内容容器",children:i.jsx(H,{children:i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[i.jsxs($,{shadow:"sm",hover:!0,children:[i.jsx(Gu,{title:"简单卡片",subtitle:"基础卡片示例"}),i.jsx(H,{children:i.jsx("p",{className:"text-gray-600",children:"这是一个简单的卡片内容。"})})]}),i.jsxs($,{shadow:"md",children:[i.jsx(Gu,{title:"带操作的卡片",actions:i.jsx(I,{size:"sm",variant:"outline",children:"编辑"})}),i.jsx(H,{children:i.jsx("p",{className:"text-gray-600",children:"这个卡片有头部操作按钮。"})}),i.jsx(jy,{children:i.jsxs("div",{className:"flex justify-end space-x-2",children:[i.jsx(I,{size:"sm",variant:"ghost",children:"取消"}),i.jsx(I,{size:"sm",children:"保存"})]})})]}),i.jsx($,{shadow:"lg",border:!1,children:i.jsxs(H,{padding:"lg",children:[i.jsx("h3",{className:"text-lg font-semibold mb-2",children:"无边框卡片"}),i.jsx("p",{className:"text-gray-600",children:"这个卡片没有边框，有大内边距。"})]})})]})})}),i.jsx($,{title:"加载组件 (Loading)",subtitle:"多种加载状态指示器",children:i.jsx(H,{children:i.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[i.jsxs("div",{className:"text-center",children:[i.jsx(Zt,{variant:"spinner",size:"md"}),i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"旋转加载器"})]}),i.jsxs("div",{className:"text-center",children:[i.jsx(Zt,{variant:"dots",size:"md"}),i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"点状加载器"})]}),i.jsxs("div",{className:"text-center",children:[i.jsx(Zt,{variant:"pulse",size:"md"}),i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"脉冲加载器"})]}),i.jsxs("div",{className:"text-center",children:[i.jsx(Zt,{variant:"bars",size:"md"}),i.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"条状加载器"})]})]})})}),i.jsx($,{title:"模态框组件 (Modal)",subtitle:"弹出式对话框",children:i.jsx(H,{children:i.jsxs("div",{className:"flex gap-4",children:[i.jsx(I,{onClick:()=>t(!0),children:"打开普通模态框"}),i.jsx(I,{variant:"danger",onClick:()=>r(!0),children:"打开确认对话框"})]})})}),i.jsx(Zf,{isOpen:e,onClose:()=>t(!1),title:"示例模态框",size:"md",footer:i.jsxs(i.Fragment,{children:[i.jsx(I,{variant:"ghost",onClick:()=>t(!1),children:"取消"}),i.jsx(I,{onClick:()=>t(!1),children:"确认"})]}),children:i.jsxs("div",{className:"space-y-4",children:[i.jsx("p",{children:"这是一个示例模态框的内容。"}),i.jsx(q,{label:"模态框中的输入框",placeholder:"可以在模态框中使用其他组件"})]})}),i.jsx(Py,{isOpen:n,onClose:()=>r(!1),onConfirm:()=>alert("已确认操作！"),title:"确认删除",message:"您确定要删除这个项目吗？此操作不可撤销。",variant:"danger",confirmText:"删除",cancelText:"取消"})]})},Vy=({activeTab:e})=>{const t=()=>{switch(e){case"api-settings":return i.jsx(Ku,{});case"field-mapping":return i.jsx(Iy,{});case"performance-config":return i.jsx(Ay,{});case"performance":return i.jsx(Dy,{});case"logs":return i.jsx(Fy,{});case"other-settings":return i.jsx(zy,{});case"debug":return i.jsx(Uy,{});case"help":return i.jsx(By,{});case"about-author":return i.jsx($y,{});case"components":return i.jsx(Hy,{});default:return i.jsx(Ku,{})}};return i.jsx("div",{className:"notion-wp-content",children:t()})},Wy=[{id:"api-settings",label:"🔄 同步设置",icon:"🔄"},{id:"field-mapping",label:"🔗 字段映射",icon:"🔗"},{id:"performance-config",label:"⚡ 性能配置",icon:"⚡"},{id:"performance",label:"📊 性能监控",icon:"📊"},{id:"logs",label:"📋 日志查看",icon:"📋"},{id:"other-settings",label:"⚙️ 其他设置",icon:"⚙️"},{id:"debug",label:"🐞 调试工具",icon:"🐞"},{id:"components",label:"🎨 组件展示",icon:"🎨"},{id:"help",label:"📖 使用帮助",icon:"📖"},{id:"about-author",label:"👨‍💻 关于作者",icon:"👨‍💻"}],Qy=()=>{var n;const[e,t]=M.useState("api-settings");return i.jsxs("div",{className:"notion-wp-admin",children:[i.jsx("div",{className:"notion-wp-header",children:i.jsxs("div",{className:"notion-wp-header-content",children:[i.jsxs("h1",{className:"wp-heading-inline",children:[i.jsx("span",{className:"notion-wp-logo"}),"Notion to WordPress"]}),i.jsx("div",{className:"notion-wp-version",children:((n=window.wpNotionConfig)==null?void 0:n.version)||"2.0.0-beta.1"})]})}),i.jsxs("div",{className:"notion-wp-layout",children:[i.jsx(bh,{activeTab:e,onTabChange:t,tabs:Wy}),i.jsx(Vy,{activeTab:e})]})]})},qy=(e,t=!0)=>{var c;const n=M.useRef(null),r=M.useRef(null),{updateProgress:s,updateStatus:o,handleSSEEvent:l}=Wn(),a=M.useCallback(()=>{n.current&&(n.current.close(),n.current=null),r.current&&(clearTimeout(r.current),r.current=null)},[]),u=M.useCallback(()=>{if(!(!e||!t))try{a();const m=Lt().createSSEConnection(e);n.current=m,m.onopen=g=>{console.log("🔗 [SSE Hook] 连接已建立",{taskId:e,event:g})},m.onmessage=g=>{try{const x=JSON.parse(g.data);console.log("📨 [SSE Hook] 收到消息:",x),l({type:x.type||"progress",data:x,timestamp:new Date().toISOString()})}catch(x){console.error("❌ [SSE Hook] 消息解析失败:",x,g.data)}},m.onerror=g=>{console.error("❌ [SSE Hook] 连接错误:",g),m.readyState===EventSource.CLOSED&&(console.log("🔄 [SSE Hook] 连接已断开，尝试重连..."),a(),r.current=setTimeout(()=>{u()},3e3))},m.addEventListener("progress",g=>{try{const x=JSON.parse(g.data);s(x.percentage||x.progress||0,x.message||x.current_status)}catch(x){console.error("❌ [SSE Hook] progress事件解析失败:",x)}}),m.addEventListener("status",g=>{try{const x=JSON.parse(g.data);o(x.status,{step:x.step||"unknown",status:x.status||"running",message:x.message,timestamp:new Date().toISOString(),progress:x.progress})}catch(x){console.error("❌ [SSE Hook] status事件解析失败:",x)}}),m.addEventListener("completed",g=>{try{const x=JSON.parse(g.data);o("completed",{step:"completed",status:"completed",message:x.message||"同步完成",timestamp:new Date().toISOString(),progress:100}),a()}catch(x){console.error("❌ [SSE Hook] completed事件解析失败:",x)}}),m.addEventListener("error",g=>{try{const x=JSON.parse(g.data);o("failed",{step:"error",status:"failed",message:x.message||"同步发生错误",timestamp:new Date().toISOString()}),a()}catch(x){console.error("❌ [SSE Hook] error事件解析失败:",x),o("failed",{step:"error",status:"failed",message:"同步发生未知错误",timestamp:new Date().toISOString()}),a()}})}catch(f){console.error("❌ [SSE Hook] 创建SSE连接失败:",f)}},[e,t,a,s,o,l]);return M.useEffect(()=>(e&&t?u():a(),a),[e,t,u,a]),M.useEffect(()=>a,[a]),{isConnected:((c=n.current)==null?void 0:c.readyState)===EventSource.OPEN,reconnect:u,disconnect:a}},Gy=e=>qy(e,!!e);const Ky=()=>{const{loadStats:e,taskId:t}=Wn(),{loadSettings:n}=bo();return Gy(t),M.useEffect(()=>{(async()=>{try{await Promise.all([n(),e()]),console.log("🚀 [App] 应用初始化完成")}catch(s){console.error("❌ [App] 应用初始化失败:",s)}})()},[n,e]),i.jsx("div",{className:"notion-wp-app",children:i.jsx(Qy,{})})},Xu=document.getElementById("notion-to-wordpress-react-root");Xu?cl.createRoot(Xu).render(i.jsx(uc.StrictMode,{children:i.jsx(Ky,{})})):console.error("React挂载点未找到：#notion-to-wordpress-react-root");
