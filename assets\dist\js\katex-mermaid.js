"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunknotion_to_wordpress"] = self["webpackChunknotion_to_wordpress"] || []).push([["katex-mermaid"],{

/***/ "./src/frontend/components/MathRenderer.ts":
/*!*************************************************!*\
  !*** ./src/frontend/components/MathRenderer.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MathRenderer: () => (/* binding */ MathRenderer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mathRenderer: () => (/* binding */ mathRenderer)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ \"./node_modules/core-js/modules/es.array.concat.js\");\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.join.js */ \"./node_modules/core-js/modules/es.array.join.js\");\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core-js/modules/es.string.trim.js */ \"./node_modules/core-js/modules/es.string.trim.js\");\n/* harmony import */ var core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_trim_js__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\nvar _MathRenderer;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 数学公式和图表渲染器 - 完整功能迁移版本\n *\n * 从原有katex-mermaid.js完全迁移所有功能，包括：\n * - KaTeX数学公式渲染（支持mhchem化学公式）\n * - Mermaid图表渲染\n * - 资源加载失败备用方案\n * - 智能兼容性检查\n * - 本地资源回退机制\n */\n\n\n\n// KaTeX配置选项\nvar KATEX_OPTIONS = {\n  displayMode: false,\n  throwOnError: false,\n  errorColor: '#cc0000',\n  strict: 'warn',\n  trust: false,\n  macros: {\n    '\\\\f': '#1f(#2)'\n  }\n};\n\n// Mermaid配置选项\nvar MERMAID_CONFIG = {\n  startOnLoad: false,\n  theme: 'default',\n  securityLevel: 'loose',\n  fontFamily: 'Arial, sans-serif',\n  fontSize: 14,\n  flowchart: {\n    useMaxWidth: true,\n    htmlLabels: true\n  },\n  sequence: {\n    useMaxWidth: true,\n    wrap: true\n  }\n};\n\n/**\n * 资源回退管理器\n */\nvar ResourceFallbackManager = /*#__PURE__*/function () {\n  function ResourceFallbackManager() {\n    _classCallCheck(this, ResourceFallbackManager);\n  }\n  return _createClass(ResourceFallbackManager, null, [{\n    key: \"showCompatibilityTips\",\n    value:\n    /**\n     * 显示主题兼容性检查建议\n     */\n    function showCompatibilityTips() {\n      console.group('🔧 [Notion to WordPress] 主题兼容性检查建议');\n      console.info('如果数学公式或图表显示异常，请尝试以下解决方案：');\n      console.info('1. 确认当前主题正确调用了wp_footer()函数');\n      console.info('2. 检查主题是否与其他插件存在JavaScript冲突');\n      console.info('3. 尝试切换到WordPress默认主题（如Twenty Twenty-Three）测试');\n      console.info('4. 检查浏览器控制台是否有其他错误信息');\n      console.info('5. 确认网络连接正常，CDN资源可以正常访问');\n      console.groupEnd();\n    }\n\n    /**\n     * 动态加载CSS文件\n     */\n  }, {\n    key: \"loadFallbackCSS\",\n    value: function loadFallbackCSS(localPath) {\n      return new Promise(function (resolve, reject) {\n        var link = document.createElement('link');\n        link.rel = 'stylesheet';\n        link.type = 'text/css';\n        link.href = localPath;\n        link.onload = function () {\n          console.log('✅ 备用CSS加载成功:', localPath);\n          resolve();\n        };\n        link.onerror = function () {\n          console.error('❌ 备用CSS加载失败:', localPath);\n          reject(new Error('CSS加载失败'));\n        };\n        document.head.appendChild(link);\n      });\n    }\n\n    /**\n     * 动态加载JS文件\n     */\n  }, {\n    key: \"loadFallbackJS\",\n    value: function loadFallbackJS(localPath) {\n      return new Promise(function (resolve, reject) {\n        var script = document.createElement('script');\n        script.type = 'text/javascript';\n        script.src = localPath;\n        script.onload = function () {\n          console.log('✅ 备用JS加载成功:', localPath);\n          resolve();\n        };\n        script.onerror = function () {\n          console.error('❌ 备用JS加载失败:', localPath);\n          reject(new Error('JS加载失败'));\n        };\n        document.head.appendChild(script);\n      });\n    }\n\n    /**\n     * 按顺序加载KaTeX相关文件\n     */\n  }, {\n    key: \"loadKatexFallback\",\n    value: (function () {\n      var _loadKatexFallback = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var basePath, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/katex/';\n              console.info('📦 [Notion to WordPress] 开始加载KaTeX本地备用资源...');\n              _context.p = 1;\n              _context.n = 2;\n              return this.loadFallbackCSS(basePath + 'katex.min.css');\n            case 2:\n              _context.n = 3;\n              return this.loadFallbackJS(basePath + 'katex.min.js');\n            case 3:\n              _context.n = 4;\n              return this.loadFallbackJS(basePath + 'mhchem.min.js');\n            case 4:\n              console.log('✅ [Notion to WordPress] KaTeX本地资源加载完成');\n              _context.n = 6;\n              break;\n            case 5:\n              _context.p = 5;\n              _t = _context.v;\n              console.error('❌ [Notion to WordPress] KaTeX本地资源加载失败:', _t);\n              throw _t;\n            case 6:\n              return _context.a(2);\n          }\n        }, _callee, this, [[1, 5]]);\n      }));\n      function loadKatexFallback() {\n        return _loadKatexFallback.apply(this, arguments);\n      }\n      return loadKatexFallback;\n    }()\n    /**\n     * 加载Mermaid备用资源\n     */\n    )\n  }, {\n    key: \"loadMermaidFallback\",\n    value: (function () {\n      var _loadMermaidFallback = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var basePath, _t2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.p = _context2.n) {\n            case 0:\n              basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/mermaid/';\n              console.info('📦 [Notion to WordPress] 开始加载Mermaid本地备用资源...');\n              _context2.p = 1;\n              _context2.n = 2;\n              return this.loadFallbackJS(basePath + 'mermaid.min.js');\n            case 2:\n              console.log('✅ [Notion to WordPress] Mermaid本地资源加载完成');\n              _context2.n = 4;\n              break;\n            case 3:\n              _context2.p = 3;\n              _t2 = _context2.v;\n              console.error('❌ [Notion to WordPress] Mermaid本地资源加载失败:', _t2);\n              throw _t2;\n            case 4:\n              return _context2.a(2);\n          }\n        }, _callee2, this, [[1, 3]]);\n      }));\n      function loadMermaidFallback() {\n        return _loadMermaidFallback.apply(this, arguments);\n      }\n      return loadMermaidFallback;\n    }())\n  }]);\n}();\nvar MathRenderer = /*#__PURE__*/function () {\n  function MathRenderer() {\n    _classCallCheck(this, MathRenderer);\n    _defineProperty(this, \"katexLoaded\", false);\n    _defineProperty(this, \"mermaidLoaded\", false);\n    _defineProperty(this, \"katexLoadPromise\", null);\n    _defineProperty(this, \"mermaidLoadPromise\", null);\n    if (MathRenderer.instance) {\n      return MathRenderer.instance;\n    }\n    MathRenderer.instance = this;\n    this.init();\n  }\n  return _createClass(MathRenderer, [{\n    key: \"init\",\n    value: function init() {\n      var _this = this;\n      // 监听数学公式渲染事件\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.on('frontend:math:render', this.renderMath.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.on('frontend:mermaid:render', this.renderMermaid.bind(this));\n\n      // 页面加载完成后自动检测和渲染\n      if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', function () {\n          _this.detectAndRender();\n        });\n      } else {\n        this.detectAndRender();\n      }\n      console.log('🧮 [数学渲染器] 已初始化');\n    }\n\n    /**\n     * 检测并渲染页面中的数学公式和图表\n     */\n  }, {\n    key: \"detectAndRender\",\n    value: function detectAndRender() {\n      var _this2 = this;\n      // 检测KaTeX公式 - 支持多种选择器\n      var mathSelectors = ['.notion-equation', '.katex-math', '.math-expression', '[data-math]', '.wp-block-notion-math'];\n      var mathElements = document.querySelectorAll(mathSelectors.join(', '));\n      if (mathElements.length > 0) {\n        console.log(\"\\uD83E\\uDDEE \\u53D1\\u73B0 \".concat(mathElements.length, \" \\u4E2A\\u6570\\u5B66\\u516C\\u5F0F\\u5143\\u7D20\"));\n        this.loadKaTeX().then(function () {\n          mathElements.forEach(function (element) {\n            _this2.renderMathElement(element);\n          });\n        }).catch(function (error) {\n          console.error('KaTeX加载失败:', error);\n          ResourceFallbackManager.showCompatibilityTips();\n        });\n      }\n\n      // 检测Mermaid图表 - 支持多种选择器\n      var mermaidSelectors = ['.notion-mermaid', '.mermaid-chart', '.diagram', '[data-mermaid]', '.wp-block-notion-mermaid'];\n      var mermaidElements = document.querySelectorAll(mermaidSelectors.join(', '));\n      if (mermaidElements.length > 0) {\n        console.log(\"\\uD83D\\uDCCA \\u53D1\\u73B0 \".concat(mermaidElements.length, \" \\u4E2A\\u56FE\\u8868\\u5143\\u7D20\"));\n        this.loadMermaid().then(function () {\n          mermaidElements.forEach(function (element) {\n            _this2.renderMermaidElement(element);\n          });\n        }).catch(function (error) {\n          console.error('Mermaid加载失败:', error);\n          ResourceFallbackManager.showCompatibilityTips();\n        });\n      }\n\n      // 检测化学公式\n      var chemElements = document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]');\n      if (chemElements.length > 0) {\n        console.log(\"\\uD83E\\uDDEA \\u53D1\\u73B0 \".concat(chemElements.length, \" \\u4E2A\\u5316\\u5B66\\u516C\\u5F0F\\u5143\\u7D20\"));\n        this.loadKaTeX().then(function () {\n          chemElements.forEach(function (element) {\n            _this2.renderChemistryElement(element);\n          });\n        }).catch(function (error) {\n          console.error('化学公式渲染失败:', error);\n        });\n      }\n    }\n\n    /**\n     * 加载KaTeX库（支持CDN和本地回退）\n     */\n  }, {\n    key: \"loadKaTeX\",\n    value: (function () {\n      var _loadKaTeX = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              if (!this.katexLoaded) {\n                _context3.n = 1;\n                break;\n              }\n              return _context3.a(2);\n            case 1:\n              if (!this.katexLoadPromise) {\n                _context3.n = 2;\n                break;\n              }\n              return _context3.a(2, this.katexLoadPromise);\n            case 2:\n              if (!window.katex) {\n                _context3.n = 3;\n                break;\n              }\n              this.katexLoaded = true;\n              return _context3.a(2);\n            case 3:\n              console.log('📦 [KaTeX] 开始加载KaTeX资源...');\n              this.katexLoadPromise = this.performKatexLoad();\n              return _context3.a(2, this.katexLoadPromise);\n          }\n        }, _callee3, this);\n      }));\n      function loadKaTeX() {\n        return _loadKaTeX.apply(this, arguments);\n      }\n      return loadKaTeX;\n    }()\n    /**\n     * 执行KaTeX加载\n     */\n    )\n  }, {\n    key: \"performKatexLoad\",\n    value: (function () {\n      var _performKatexLoad = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var _t3, _t4;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.p = _context4.n) {\n            case 0:\n              _context4.p = 0;\n              _context4.n = 1;\n              return this.loadKatexFromCDN();\n            case 1:\n              console.log('✅ [KaTeX] CDN资源加载成功');\n              _context4.n = 6;\n              break;\n            case 2:\n              _context4.p = 2;\n              _t3 = _context4.v;\n              console.warn('⚠️ [KaTeX] CDN加载失败，尝试本地资源:', _t3);\n              _context4.p = 3;\n              _context4.n = 4;\n              return ResourceFallbackManager.loadKatexFallback();\n            case 4:\n              console.log('✅ [KaTeX] 本地资源加载成功');\n              _context4.n = 6;\n              break;\n            case 5:\n              _context4.p = 5;\n              _t4 = _context4.v;\n              console.error('❌ [KaTeX] 本地资源也加载失败:', _t4);\n              ResourceFallbackManager.showCompatibilityTips();\n              throw new Error('KaTeX加载完全失败');\n            case 6:\n              if (window.katex) {\n                _context4.n = 7;\n                break;\n              }\n              throw new Error('KaTeX加载后仍不可用');\n            case 7:\n              this.katexLoaded = true;\n              console.log('🎉 [KaTeX] 加载完成并可用');\n            case 8:\n              return _context4.a(2);\n          }\n        }, _callee4, this, [[3, 5], [0, 2]]);\n      }));\n      function performKatexLoad() {\n        return _performKatexLoad.apply(this, arguments);\n      }\n      return performKatexLoad;\n    }()\n    /**\n     * 从CDN加载KaTeX\n     */\n    )\n  }, {\n    key: \"loadKatexFromCDN\",\n    value: (function () {\n      var _loadKatexFromCDN = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var CDN_BASE, cssPromise, jsPromise, mhchemPromise;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              CDN_BASE = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/'; // 1. 加载CSS\n              cssPromise = new Promise(function (resolve, reject) {\n                var link = document.createElement('link');\n                link.rel = 'stylesheet';\n                link.href = CDN_BASE + 'katex.min.css';\n                link.onload = function () {\n                  return resolve();\n                };\n                link.onerror = function () {\n                  return reject(new Error('KaTeX CSS加载失败'));\n                };\n                document.head.appendChild(link);\n              }); // 2. 加载主JS\n              jsPromise = new Promise(function (resolve, reject) {\n                var script = document.createElement('script');\n                script.src = CDN_BASE + 'katex.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  return reject(new Error('KaTeX JS加载失败'));\n                };\n                document.head.appendChild(script);\n              }); // 等待CSS和JS都加载完成\n              _context5.n = 1;\n              return Promise.all([cssPromise, jsPromise]);\n            case 1:\n              // 3. 加载mhchem扩展（化学公式支持）\n              mhchemPromise = new Promise(function (resolve) {\n                var script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  console.warn('mhchem扩展加载失败，化学公式功能可能不可用');\n                  resolve(); // 不阻塞主要功能\n                };\n                document.head.appendChild(script);\n              });\n              _context5.n = 2;\n              return mhchemPromise;\n            case 2:\n              return _context5.a(2);\n          }\n        }, _callee5);\n      }));\n      function loadKatexFromCDN() {\n        return _loadKatexFromCDN.apply(this, arguments);\n      }\n      return loadKatexFromCDN;\n    }()\n    /**\n     * 加载Mermaid库（支持CDN和本地回退）\n     */\n    )\n  }, {\n    key: \"loadMermaid\",\n    value: (function () {\n      var _loadMermaid = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              if (!this.mermaidLoaded) {\n                _context6.n = 1;\n                break;\n              }\n              return _context6.a(2);\n            case 1:\n              if (!this.mermaidLoadPromise) {\n                _context6.n = 2;\n                break;\n              }\n              return _context6.a(2, this.mermaidLoadPromise);\n            case 2:\n              if (!window.mermaid) {\n                _context6.n = 3;\n                break;\n              }\n              this.mermaidLoaded = true;\n              return _context6.a(2);\n            case 3:\n              console.log('📊 [Mermaid] 开始加载Mermaid资源...');\n              this.mermaidLoadPromise = this.performMermaidLoad();\n              return _context6.a(2, this.mermaidLoadPromise);\n          }\n        }, _callee6, this);\n      }));\n      function loadMermaid() {\n        return _loadMermaid.apply(this, arguments);\n      }\n      return loadMermaid;\n    }()\n    /**\n     * 执行Mermaid加载\n     */\n    )\n  }, {\n    key: \"performMermaidLoad\",\n    value: (function () {\n      var _performMermaidLoad = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {\n        var _t5, _t6;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.p = _context7.n) {\n            case 0:\n              _context7.p = 0;\n              _context7.n = 1;\n              return this.loadMermaidFromCDN();\n            case 1:\n              console.log('✅ [Mermaid] CDN资源加载成功');\n              _context7.n = 6;\n              break;\n            case 2:\n              _context7.p = 2;\n              _t5 = _context7.v;\n              console.warn('⚠️ [Mermaid] CDN加载失败，尝试本地资源:', _t5);\n              _context7.p = 3;\n              _context7.n = 4;\n              return ResourceFallbackManager.loadMermaidFallback();\n            case 4:\n              console.log('✅ [Mermaid] 本地资源加载成功');\n              _context7.n = 6;\n              break;\n            case 5:\n              _context7.p = 5;\n              _t6 = _context7.v;\n              console.error('❌ [Mermaid] 本地资源也加载失败:', _t6);\n              ResourceFallbackManager.showCompatibilityTips();\n              throw new Error('Mermaid加载完全失败');\n            case 6:\n              if (window.mermaid) {\n                _context7.n = 7;\n                break;\n              }\n              throw new Error('Mermaid加载后仍不可用');\n            case 7:\n              // 初始化Mermaid配置\n              window.mermaid.initialize(MERMAID_CONFIG);\n              this.mermaidLoaded = true;\n              console.log('🎉 [Mermaid] 加载完成并可用');\n            case 8:\n              return _context7.a(2);\n          }\n        }, _callee7, this, [[3, 5], [0, 2]]);\n      }));\n      function performMermaidLoad() {\n        return _performMermaidLoad.apply(this, arguments);\n      }\n      return performMermaidLoad;\n    }()\n    /**\n     * 从CDN加载Mermaid\n     */\n    )\n  }, {\n    key: \"loadMermaidFromCDN\",\n    value: (function () {\n      var _loadMermaidFromCDN = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              return _context8.a(2, new Promise(function (resolve, reject) {\n                var script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';\n                script.onload = function () {\n                  return resolve();\n                };\n                script.onerror = function () {\n                  return reject(new Error('Mermaid CDN加载失败'));\n                };\n                document.head.appendChild(script);\n              }));\n          }\n        }, _callee8);\n      }));\n      function loadMermaidFromCDN() {\n        return _loadMermaidFromCDN.apply(this, arguments);\n      }\n      return loadMermaidFromCDN;\n    }()\n    /**\n     * 渲染数学公式（事件处理器）\n     */\n    )\n  }, {\n    key: \"renderMath\",\n    value: function renderMath(_event, data) {\n      this.renderMathElement(data.element);\n    }\n\n    /**\n     * 渲染图表（事件处理器）\n     */\n  }, {\n    key: \"renderMermaid\",\n    value: function renderMermaid(_event, data) {\n      this.renderMermaidElement(data.element);\n    }\n\n    /**\n     * 渲染单个数学公式元素\n     */\n  }, {\n    key: \"renderMathElement\",\n    value: function renderMathElement(element) {\n      if (!this.katexLoaded || !window.katex) {\n        console.warn('KaTeX未加载，无法渲染数学公式');\n        return;\n      }\n\n      // 获取数学表达式\n      var expression = element.textContent || element.getAttribute('data-expression') || element.getAttribute('data-math') || element.innerHTML;\n      if (!expression || expression.trim() === '') {\n        console.warn('数学表达式为空，跳过渲染');\n        return;\n      }\n      try {\n        // 判断是否为行内公式\n        var isInline = element.classList.contains('inline') || element.classList.contains('katex-inline') || element.hasAttribute('data-inline');\n\n        // 使用完整的KaTeX配置\n        var options = _objectSpread(_objectSpread({}, KATEX_OPTIONS), {}, {\n          displayMode: !isInline,\n          throwOnError: false,\n          errorColor: '#cc0000',\n          strict: 'warn'\n        });\n\n        // 渲染数学公式\n        window.katex.render(expression, element, options);\n\n        // 添加成功渲染的标记\n        element.classList.add('katex-rendered');\n        element.setAttribute('data-rendered', 'true');\n        console.log('✅ 数学公式渲染成功:', expression.substring(0, 50) + '...');\n      } catch (error) {\n        console.error('❌ KaTeX渲染错误:', error);\n\n        // 显示错误信息\n        element.innerHTML = \"\\n        <span style=\\\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\\\">\\n          \\u6570\\u5B66\\u516C\\u5F0F\\u9519\\u8BEF: \".concat(expression.substring(0, 100)).concat(expression.length > 100 ? '...' : '', \"\\n        </span>\\n      \");\n        element.classList.add('katex-error');\n      }\n    }\n\n    /**\n     * 渲染化学公式元素\n     */\n  }, {\n    key: \"renderChemistryElement\",\n    value: function renderChemistryElement(element) {\n      if (!this.katexLoaded || !window.katex) {\n        console.warn('KaTeX未加载，无法渲染化学公式');\n        return;\n      }\n\n      // 获取化学表达式\n      var expression = element.textContent || element.getAttribute('data-chemistry') || element.getAttribute('data-chem');\n      if (!expression || expression.trim() === '') {\n        console.warn('化学表达式为空，跳过渲染');\n        return;\n      }\n      try {\n        // 化学公式通常使用mhchem语法，需要包装在\\ce{}中\n        var chemExpression = expression.startsWith('\\\\ce{') ? expression : \"\\\\ce{\".concat(expression, \"}\");\n        var options = _objectSpread(_objectSpread({}, KATEX_OPTIONS), {}, {\n          displayMode: false,\n          throwOnError: false\n        });\n        window.katex.render(chemExpression, element, options);\n        element.classList.add('chemistry-rendered');\n        element.setAttribute('data-rendered', 'true');\n        console.log('✅ 化学公式渲染成功:', expression);\n      } catch (error) {\n        console.error('❌ 化学公式渲染错误:', error);\n        element.innerHTML = \"\\n        <span style=\\\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\\\">\\n          \\u5316\\u5B66\\u516C\\u5F0F\\u9519\\u8BEF: \".concat(expression, \"\\n        </span>\\n      \");\n        element.classList.add('chemistry-error');\n      }\n    }\n\n    /**\n     * 渲染单个Mermaid图表元素\n     */\n  }, {\n    key: \"renderMermaidElement\",\n    value: function renderMermaidElement(element) {\n      if (!this.mermaidLoaded || !window.mermaid) {\n        console.warn('Mermaid未加载，无法渲染图表');\n        return;\n      }\n\n      // 获取图表代码\n      var diagram = element.textContent || element.getAttribute('data-mermaid') || element.getAttribute('data-code') || element.getAttribute('data-diagram') || element.innerHTML;\n      if (!diagram || diagram.trim() === '') {\n        console.warn('图表代码为空，跳过渲染');\n        return;\n      }\n      try {\n        // 生成唯一ID（使用现代方法替代已弃用的substr）\n        var id = \"mermaid-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substring(2, 11));\n\n        // 清空元素内容，显示加载状态\n        element.innerHTML = '<div class=\"mermaid-loading\">正在渲染图表...</div>';\n        element.classList.add('mermaid-rendering');\n\n        // 渲染图表\n        window.mermaid.render(id, diagram).then(function (result) {\n          // 渲染成功\n          element.innerHTML = result.svg;\n          element.classList.remove('mermaid-rendering');\n          element.classList.add('mermaid-rendered');\n          element.setAttribute('data-rendered', 'true');\n\n          // 添加响应式支持\n          var svg = element.querySelector('svg');\n          if (svg) {\n            svg.style.maxWidth = '100%';\n            svg.style.height = 'auto';\n          }\n          console.log('✅ Mermaid图表渲染成功');\n        }).catch(function (error) {\n          // 渲染失败\n          console.error('❌ Mermaid渲染错误:', error);\n          element.innerHTML = \"\\n          <div style=\\\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\\\">\\n            <strong>\\u56FE\\u8868\\u6E32\\u67D3\\u9519\\u8BEF</strong><br>\\n            <small>\".concat(error.message || '未知错误', \"</small><br>\\n            <details style=\\\"margin-top: 5px;\\\">\\n              <summary style=\\\"cursor: pointer;\\\">\\u67E5\\u770B\\u539F\\u59CB\\u4EE3\\u7801</summary>\\n              <pre style=\\\"background: #f5f5f5; padding: 5px; margin-top: 5px; border-radius: 3px; font-size: 12px;\\\">\").concat(diagram, \"</pre>\\n            </details>\\n          </div>\\n        \");\n          element.classList.remove('mermaid-rendering');\n          element.classList.add('mermaid-error');\n        });\n      } catch (error) {\n        console.error('❌ Mermaid渲染异常:', error);\n        element.innerHTML = \"\\n        <div style=\\\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\\\">\\n          <strong>\\u56FE\\u8868\\u6E32\\u67D3\\u5F02\\u5E38</strong><br>\\n          <small>\\u8BF7\\u68C0\\u67E5\\u56FE\\u8868\\u8BED\\u6CD5\\u662F\\u5426\\u6B63\\u786E</small>\\n        </div>\\n      \";\n        element.classList.add('mermaid-error');\n      }\n    }\n\n    /**\n     * 手动渲染指定元素\n     */\n  }, {\n    key: \"renderElement\",\n    value: function renderElement(element) {\n      var _this3 = this;\n      if (element.classList.contains('notion-equation') || element.classList.contains('katex-math') || element.hasAttribute('data-math')) {\n        this.loadKaTeX().then(function () {\n          _this3.renderMathElement(element);\n        }).catch(console.error);\n      } else if (element.classList.contains('notion-mermaid') || element.classList.contains('mermaid-chart') || element.hasAttribute('data-mermaid')) {\n        this.loadMermaid().then(function () {\n          _this3.renderMermaidElement(element);\n        }).catch(console.error);\n      } else if (element.classList.contains('notion-chemistry') || element.classList.contains('chemistry') || element.hasAttribute('data-chemistry')) {\n        this.loadKaTeX().then(function () {\n          _this3.renderChemistryElement(element);\n        }).catch(console.error);\n      }\n    }\n\n    /**\n     * 重新渲染所有元素\n     */\n  }, {\n    key: \"reRenderAll\",\n    value: function reRenderAll() {\n      console.log('🔄 重新渲染所有数学公式和图表...');\n      this.detectAndRender();\n    }\n\n    /**\n     * 获取渲染状态\n     */\n  }, {\n    key: \"getStatus\",\n    value: function getStatus() {\n      return {\n        katexLoaded: this.katexLoaded,\n        mermaidLoaded: this.mermaidLoaded,\n        mathElements: document.querySelectorAll('.notion-equation, .katex-math, [data-math]').length,\n        mermaidElements: document.querySelectorAll('.notion-mermaid, .mermaid-chart, [data-mermaid]').length,\n        chemElements: document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]').length\n      };\n    }\n\n    /**\n     * 销毁实例\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.off('frontend:math:render', this.renderMath.bind(this));\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.eventBus.off('frontend:mermaid:render', this.renderMermaid.bind(this));\n      MathRenderer.instance = null;\n      console.log('🧮 [数学渲染器] 已销毁');\n    }\n\n    /**\n     * 获取单例实例\n     */\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance() {\n      if (!MathRenderer.instance) {\n        MathRenderer.instance = new MathRenderer();\n      }\n      return MathRenderer.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_MathRenderer = MathRenderer;\n_defineProperty(MathRenderer, \"instance\", null);\nvar mathRenderer = new MathRenderer();\n\n// 自动初始化（兼容原有行为）\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', function () {\n    MathRenderer.getInstance();\n  });\n} else {\n  MathRenderer.getInstance();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MathRenderer);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/MathRenderer.ts\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common"], () => (__webpack_exec__("./src/frontend/components/MathRenderer.ts")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);