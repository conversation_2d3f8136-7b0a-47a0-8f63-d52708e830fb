"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunknotion_to_wordpress"] = self["webpackChunknotion_to_wordpress"] || []).push([["frontend"],{

/***/ "./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[2].use[3]!./src/styles/frontend/frontend.scss":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[2].use[3]!./src/styles/frontend/frontend.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/sourceMaps.js */ \"./node_modules/css-loader/dist/runtime/sourceMaps.js\");\n/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ \"./node_modules/css-loader/dist/runtime/api.js\");\n/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `@charset \"UTF-8\";\n/**\n * 前端样式\n */\n/**\n * SCSS变量定义\n */\n.notion-block {\n  margin-bottom: 1rem;\n}\n.notion-block:last-child {\n  margin-bottom: 0;\n}\n\n.notion-paragraph {\n  line-height: 1.75;\n  color: #212121;\n}\n\n.notion-heading {\n  font-weight: 600;\n  line-height: 1.25;\n  margin-bottom: 0.75rem;\n  color: #212121;\n}\n.notion-heading-1 {\n  font-size: 1.875rem;\n  margin-bottom: 1rem;\n}\n.notion-heading-2 {\n  font-size: 1.5rem;\n  margin-bottom: 0.75rem;\n}\n.notion-heading-3 {\n  font-size: 1.25rem;\n  margin-bottom: 0.5rem;\n}\n\n.notion-list {\n  padding-left: 1.5rem;\n}\n.notion-list-item {\n  margin-bottom: 0.5rem;\n  line-height: 1.75;\n}\n.notion-list.bulleted {\n  list-style-type: disc;\n}\n.notion-list.numbered {\n  list-style-type: decimal;\n}\n.notion-list.todo {\n  list-style: none;\n  padding-left: 0;\n}\n.notion-list.todo .notion-list-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.5rem;\n}\n.notion-list.todo .notion-list-item::before {\n  content: \"☐\";\n  flex-shrink: 0;\n  margin-top: 2px;\n}\n.notion-list.todo .notion-list-item.checked::before {\n  content: \"☑\";\n  color: #46b450;\n}\n\n.notion-quote {\n  border-left: 4px solid #d1d1d1;\n  padding-left: 1rem;\n  margin: 1rem 0;\n  font-style: italic;\n  color: #616161;\n  background: #f9f9f9;\n  padding: 1rem;\n  border-radius: 0.25rem;\n}\n\n.notion-code {\n  background: #212121;\n  color: #ffffff;\n  padding: 1rem;\n  border-radius: 0.25rem;\n  font-family: Consolas, Monaco, \"Courier New\", monospace;\n  font-size: 0.875rem;\n  line-height: 1.75;\n  overflow-x: auto;\n  margin: 1rem 0;\n}\n.notion-code.inline {\n  display: inline;\n  padding: 0.25rem 0.5rem;\n  margin: 0;\n  background: #f1f1f1;\n  color: #212121;\n  border-radius: 0.125rem;\n}\n\n.notion-divider {\n  border: none;\n  border-top: 1px solid #e1e1e1;\n  margin: 1.5rem 0;\n}\n\n.notion-image {\n  margin: 1rem 0;\n  text-align: center;\n}\n.notion-image img {\n  max-width: 100%;\n  height: auto;\n  border-radius: 0.25rem;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  transition: transform 0.3s ease-in-out;\n}\n.notion-image img:hover {\n  transform: scale(1.02);\n}\n.notion-image img[data-src] {\n  opacity: 0.5;\n  filter: blur(2px);\n  transition: all 0.3s ease-in-out;\n}\n.notion-image img[data-src].loaded {\n  opacity: 1;\n  filter: none;\n}\n.notion-image .caption {\n  margin-top: 0.5rem;\n  font-size: 0.875rem;\n  color: #a1a1a1;\n  font-style: italic;\n}\n\n.notion-video {\n  margin: 1rem 0;\n  position: relative;\n  padding-bottom: 56.25%;\n  height: 0;\n  overflow: hidden;\n  border-radius: 0.25rem;\n}\n.notion-video iframe,\n.notion-video video {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: none;\n}\n\n.notion-bookmark {\n  border: 1px solid #e1e1e1;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  margin: 1rem 0;\n  display: flex;\n  gap: 1rem;\n  text-decoration: none;\n  color: inherit;\n  transition: all 0.15s ease-in-out;\n}\n.notion-bookmark:hover {\n  border-color: #0073aa;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  transform: translateY(-1px);\n}\n.notion-bookmark-content {\n  flex: 1;\n}\n.notion-bookmark-content .title {\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n  color: #212121;\n}\n.notion-bookmark-content .description {\n  font-size: 0.875rem;\n  color: #616161;\n  margin-bottom: 0.5rem;\n  line-height: 1.5;\n}\n.notion-bookmark-content .url {\n  font-size: 0.75rem;\n  color: #a1a1a1;\n  text-decoration: none;\n}\n.notion-bookmark-image {\n  flex: 0 0 120px;\n  height: 80px;\n  border-radius: 0.125rem;\n  overflow: hidden;\n}\n.notion-bookmark-image img {\n  width: 100%;\n  height: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n\n.notion-callout {\n  display: flex;\n  gap: 0.75rem;\n  padding: 1rem;\n  border-radius: 0.25rem;\n  margin: 1rem 0;\n  background: #f9f9f9;\n  border-left: 4px solid #0073aa;\n}\n.notion-callout-icon {\n  flex-shrink: 0;\n  font-size: 1.125rem;\n}\n.notion-callout-content {\n  flex: 1;\n  line-height: 1.75;\n}\n.notion-callout.info {\n  background: rgba(0, 160, 210, 0.1);\n  border-left-color: #00a0d2;\n}\n.notion-callout.warning {\n  background: rgba(255, 185, 0, 0.1);\n  border-left-color: #ffb900;\n}\n.notion-callout.error {\n  background: rgba(220, 50, 50, 0.1);\n  border-left-color: #dc3232;\n}\n.notion-callout.success {\n  background: rgba(70, 180, 80, 0.1);\n  border-left-color: #46b450;\n}\n\n.notion-equation {\n  margin: 1rem 0;\n  text-align: center;\n  overflow-x: auto;\n}\n.notion-equation.inline {\n  display: inline;\n  margin: 0;\n}\n.notion-equation .katex {\n  font-size: 1.1em;\n}\n\n.notion-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 1rem 0;\n  border-radius: 0.25rem;\n  overflow: hidden;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n.notion-table th,\n.notion-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid #e1e1e1;\n}\n.notion-table th {\n  background: #f9f9f9;\n  font-weight: 600;\n  color: #212121;\n}\n.notion-table tr:hover {\n  background: #f9f9f9;\n}\n.notion-table tr:last-child td {\n  border-bottom: none;\n}\n\n.notion-toggle {\n  margin: 0.5rem 0;\n}\n.notion-toggle-trigger {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 0.125rem;\n  transition: background-color 0.15s ease-in-out;\n}\n.notion-toggle-trigger:hover {\n  background: #f9f9f9;\n}\n.notion-toggle-trigger .toggle-icon {\n  transition: transform 0.15s ease-in-out;\n}\n.notion-toggle-trigger .toggle-icon.expanded {\n  transform: rotate(90deg);\n}\n.notion-toggle-content {\n  padding-left: 1.5rem;\n  margin-top: 0.5rem;\n  display: none;\n}\n.notion-toggle-content.expanded {\n  display: block;\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    max-height: 1000px;\n  }\n}\n@media (max-width: 768px) {\n  .notion-heading-1 {\n    font-size: 1.5rem;\n  }\n  .notion-heading-2 {\n    font-size: 1.25rem;\n  }\n  .notion-heading-3 {\n    font-size: 1.125rem;\n  }\n  .notion-bookmark {\n    flex-direction: column;\n  }\n  .notion-bookmark-image {\n    flex: none;\n    height: 200px;\n  }\n  .notion-table {\n    font-size: 0.875rem;\n  }\n  .notion-table th,\n  .notion-table td {\n    padding: 0.5rem;\n  }\n}`, \"\",{\"version\":3,\"sources\":[\"webpack://./src/styles/frontend/frontend.scss\",\"webpack://./src/styles/shared/variables.scss\"],\"names\":[],\"mappings\":\"AAAA,gBAAgB;AAAhB;;EAAA;ACAA;;EAAA;ADOA;EACE,mBCmEU;ADlEZ;AACE;EACE,gBAAA;AACJ;;AAIA;EACE,iBCmDoB;EDlDpB,cCMS;ADPX;;AAKA;EACE,gBCuCqB;EDtCrB,iBC0CkB;EDzClB,sBCiDU;EDhDV,cCFS;ADAX;AAIE;EACE,mBC0BY;EDzBZ,mBC6CQ;AD/CZ;AAKE;EACE,iBCoBY;EDnBZ,sBCuCQ;AD1CZ;AAME;EACE,kBCcW;EDbX,qBCiCQ;ADrCZ;;AASA;EACE,oBC+BU;ADrCZ;AAQE;EACE,qBCwBQ;EDvBR,iBCkBkB;ADxBtB;AASE;EACE,qBAAA;AAPJ;AAUE;EACE,wBAAA;AARJ;AAWE;EACE,gBAAA;EACA,eAAA;AATJ;AAWI;EACE,aAAA;EACA,uBAAA;EACA,WCKM;ADdZ;AAWM;EACE,YAAA;EACA,cAAA;EACA,eAAA;AATR;AAYM;EACE,YAAA;EACA,cCtEQ;AD4DhB;;AAiBA;EACE,8BAAA;EACA,kBCZU;EDaV,cAAA;EACA,kBAAA;EACA,cCpES;EDqET,mBC5EQ;ED6ER,aCjBU;EDkBV,sBCNmB;ADRrB;;AAkBA;EACE,mBC1ES;ED2ET,cCrFM;EDsFN,aCzBU;ED0BV,sBCdmB;EDenB,uDCxDiB;EDyDjB,mBCrDa;EDsDb,iBCpCoB;EDqCpB,gBAAA;EACA,cAAA;AAfF;AAiBE;EACE,eAAA;EACA,uBAAA;EACA,SAAA;EACA,mBChGO;EDiGP,cCzFO;ED0FP,uBC5Be;ADanB;;AAoBA;EACE,YAAA;EACA,6BAAA;EACA,gBAAA;AAjBF;;AAqBA;EACE,cAAA;EACA,kBAAA;AAlBF;AAoBE;EACE,eAAA;EACA,YAAA;EACA,sBC9CiB;ED+CjB,2CCvCQ;EDwCR,sCAAA;AAlBJ;AAoBI;EACE,sBAAA;AAlBN;AAqBI;EACE,YAAA;EACA,iBAAA;EACA,gCAAA;AAnBN;AAqBM;EACE,UAAA;EACA,YAAA;AAnBR;AAwBE;EACE,kBCjFQ;EDkFR,mBCzGW;ED0GX,cCxIO;EDyIP,kBAAA;AAtBJ;;AA2BA;EACE,cAAA;EACA,kBAAA;EACA,sBAAA;EACA,SAAA;EACA,gBAAA;EACA,sBCjFmB;ADyDrB;AA0BE;;EAEE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EACA,YAAA;AAxBJ;;AA6BA;EACE,yBAAA;EACA,sBCjGmB;EDkGnB,aC9GU;ED+GV,cAAA;EACA,aAAA;EACA,SCjHU;EDkHV,qBAAA;EACA,cAAA;EACA,iCAAA;AA1BF;AA4BE;EACE,qBC7LY;ED8LZ,2CCpGQ;EDqGR,2BAAA;AA1BJ;AA6BE;EACE,OAAA;AA3BJ;AA6BI;EACE,gBC7IiB;ED8IjB,sBCpIM;EDqIN,cCrLK;AD0JX;AA8BI;EACE,mBC/JS;EDgKT,cC5LK;ED6LL,qBC1IM;ED2IN,gBCjJe;ADqHrB;AA+BI;EACE,kBCvKS;EDwKT,cCrMK;EDsML,qBAAA;AA7BN;AAiCE;EACE,eAAA;EACA,YAAA;EACA,uBC3Ie;ED4If,gBAAA;AA/BJ;AAiCI;EACE,WAAA;EACA,YAAA;EACA,oBAAA;KAAA,iBAAA;AA/BN;;AAqCA;EACE,aAAA;EACA,YCrKU;EDsKV,aCrKU;EDsKV,sBC1JmB;ED2JnB,cAAA;EACA,mBCpOQ;EDqOR,8BAAA;AAlCF;AAoCE;EACE,cAAA;EACA,mBCpMW;ADkKf;AAqCE;EACE,OAAA;EACA,iBCzLkB;ADsJtB;AAuCE;EACE,kCAAA;EACA,0BCxPS;ADmNb;AAwCE;EACE,kCAAA;EACA,0BC/PY;ADyNhB;AAyCE;EACE,kCAAA;EACA,0BCnQU;AD4Nd;AA0CE;EACE,kCAAA;EACA,0BC1QY;ADkOhB;;AA6CA;EACE,cAAA;EACA,kBAAA;EACA,gBAAA;AA1CF;AA4CE;EACE,eAAA;EACA,SAAA;AA1CJ;AA6CE;EACE,gBAAA;AA3CJ;;AAgDA;EACE,WAAA;EACA,yBAAA;EACA,cAAA;EACA,sBCpNmB;EDqNnB,gBAAA;EACA,2CC9MU;ADiKZ;AA+CE;;EAEE,gBCvOQ;EDwOR,gBAAA;EACA,gCAAA;AA7CJ;AAgDE;EACE,mBCxSM;EDySN,gBC1PmB;ED2PnB,cCjSO;ADmPX;AAiDE;EACE,mBC9SM;AD+PV;AAkDE;EACE,mBAAA;AAhDJ;;AAqDA;EACE,gBAAA;AAlDF;AAoDE;EACE,aAAA;EACA,mBAAA;EACA,WCnQQ;EDoQR,eAAA;EACA,eCrQQ;EDsQR,uBCzPe;ED0Pf,8CAAA;AAlDJ;AAoDI;EACE,mBCpUI;ADkRV;AAqDI;EACE,uCAAA;AAnDN;AAqDM;EACE,wBAAA;AAnDR;AAwDE;EACE,oBCnRQ;EDoRR,kBCxRQ;EDyRR,aAAA;AAtDJ;AAwDI;EACE,cAAA;EACA,kCAAA;AAtDN;;AA2DA;EACE;IACE,UAAA;IACA,aAAA;EAxDF;EA0DA;IACE,UAAA;IACA,kBAAA;EAxDF;AACF;AA4DA;EAEI;IACE,iBCpUU;EDyQd;EA8DE;IACE,kBCzUS;ED6Qb;EA+DE;IACE,mBC9US;EDiRb;EAiEA;IACE,sBAAA;EA/DF;EAiEE;IACE,UAAA;IACA,aAAA;EA/DJ;EAmEA;IACE,mBC9VW;ED6Rb;EAmEE;;IAEE,eC3UM;ED0QV;AACF\",\"sourcesContent\":[\"/**\\n * 前端样式\\n */\\n\\n@import '../shared/variables';\\n\\n// Notion块基础样式\\n.notion-block {\\n  margin-bottom: $spacing-4;\\n  \\n  &:last-child {\\n    margin-bottom: 0;\\n  }\\n}\\n\\n// 段落块\\n.notion-paragraph {\\n  line-height: $line-height-relaxed;\\n  color: $text-primary;\\n}\\n\\n// 标题块\\n.notion-heading {\\n  font-weight: $font-weight-semibold;\\n  line-height: $line-height-tight;\\n  margin-bottom: $spacing-3;\\n  color: $text-primary;\\n\\n  &-1 {\\n    font-size: $font-size-3xl;\\n    margin-bottom: $spacing-4;\\n  }\\n\\n  &-2 {\\n    font-size: $font-size-2xl;\\n    margin-bottom: $spacing-3;\\n  }\\n\\n  &-3 {\\n    font-size: $font-size-xl;\\n    margin-bottom: $spacing-2;\\n  }\\n}\\n\\n// 列表块\\n.notion-list {\\n  padding-left: $spacing-6;\\n  \\n  &-item {\\n    margin-bottom: $spacing-2;\\n    line-height: $line-height-relaxed;\\n  }\\n\\n  &.bulleted {\\n    list-style-type: disc;\\n  }\\n\\n  &.numbered {\\n    list-style-type: decimal;\\n  }\\n\\n  &.todo {\\n    list-style: none;\\n    padding-left: 0;\\n\\n    .notion-list-item {\\n      display: flex;\\n      align-items: flex-start;\\n      gap: $spacing-2;\\n\\n      &::before {\\n        content: '☐';\\n        flex-shrink: 0;\\n        margin-top: 2px;\\n      }\\n\\n      &.checked::before {\\n        content: '☑';\\n        color: $success-color;\\n      }\\n    }\\n  }\\n}\\n\\n// 引用块\\n.notion-quote {\\n  border-left: 4px solid $border-color-dark;\\n  padding-left: $spacing-4;\\n  margin: $spacing-4 0;\\n  font-style: italic;\\n  color: $text-secondary;\\n  background: $bg-secondary;\\n  padding: $spacing-4;\\n  border-radius: $border-radius-base;\\n}\\n\\n// 代码块\\n.notion-code {\\n  background: $gray-900;\\n  color: $white;\\n  padding: $spacing-4;\\n  border-radius: $border-radius-base;\\n  font-family: $font-family-mono;\\n  font-size: $font-size-sm;\\n  line-height: $line-height-relaxed;\\n  overflow-x: auto;\\n  margin: $spacing-4 0;\\n\\n  &.inline {\\n    display: inline;\\n    padding: $spacing-1 $spacing-2;\\n    margin: 0;\\n    background: $gray-100;\\n    color: $text-primary;\\n    border-radius: $border-radius-sm;\\n  }\\n}\\n\\n// 分割线\\n.notion-divider {\\n  border: none;\\n  border-top: 1px solid $border-color;\\n  margin: $spacing-6 0;\\n}\\n\\n// 图片块\\n.notion-image {\\n  margin: $spacing-4 0;\\n  text-align: center;\\n\\n  img {\\n    max-width: 100%;\\n    height: auto;\\n    border-radius: $border-radius-base;\\n    box-shadow: $shadow-sm;\\n    transition: transform $transition-base;\\n\\n    &:hover {\\n      transform: scale(1.02);\\n    }\\n\\n    &[data-src] {\\n      opacity: 0.5;\\n      filter: blur(2px);\\n      transition: all $transition-base;\\n\\n      &.loaded {\\n        opacity: 1;\\n        filter: none;\\n      }\\n    }\\n  }\\n\\n  .caption {\\n    margin-top: $spacing-2;\\n    font-size: $font-size-sm;\\n    color: $text-muted;\\n    font-style: italic;\\n  }\\n}\\n\\n// 视频块\\n.notion-video {\\n  margin: $spacing-4 0;\\n  position: relative;\\n  padding-bottom: 56.25%; // 16:9 aspect ratio\\n  height: 0;\\n  overflow: hidden;\\n  border-radius: $border-radius-base;\\n\\n  iframe,\\n  video {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 100%;\\n    border: none;\\n  }\\n}\\n\\n// 书签块\\n.notion-bookmark {\\n  border: 1px solid $border-color;\\n  border-radius: $border-radius-base;\\n  padding: $spacing-4;\\n  margin: $spacing-4 0;\\n  display: flex;\\n  gap: $spacing-4;\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all $transition-fast;\\n\\n  &:hover {\\n    border-color: $primary-color;\\n    box-shadow: $shadow-sm;\\n    transform: translateY(-1px);\\n  }\\n\\n  &-content {\\n    flex: 1;\\n\\n    .title {\\n      font-weight: $font-weight-semibold;\\n      margin-bottom: $spacing-1;\\n      color: $text-primary;\\n    }\\n\\n    .description {\\n      font-size: $font-size-sm;\\n      color: $text-secondary;\\n      margin-bottom: $spacing-2;\\n      line-height: $line-height-normal;\\n    }\\n\\n    .url {\\n      font-size: $font-size-xs;\\n      color: $text-muted;\\n      text-decoration: none;\\n    }\\n  }\\n\\n  &-image {\\n    flex: 0 0 120px;\\n    height: 80px;\\n    border-radius: $border-radius-sm;\\n    overflow: hidden;\\n\\n    img {\\n      width: 100%;\\n      height: 100%;\\n      object-fit: cover;\\n    }\\n  }\\n}\\n\\n// 标注块\\n.notion-callout {\\n  display: flex;\\n  gap: $spacing-3;\\n  padding: $spacing-4;\\n  border-radius: $border-radius-base;\\n  margin: $spacing-4 0;\\n  background: $bg-secondary;\\n  border-left: 4px solid $primary-color;\\n\\n  &-icon {\\n    flex-shrink: 0;\\n    font-size: $font-size-lg;\\n  }\\n\\n  &-content {\\n    flex: 1;\\n    line-height: $line-height-relaxed;\\n  }\\n\\n  // 不同类型的标注\\n  &.info {\\n    background: rgba($info-color, 0.1);\\n    border-left-color: $info-color;\\n  }\\n\\n  &.warning {\\n    background: rgba($warning-color, 0.1);\\n    border-left-color: $warning-color;\\n  }\\n\\n  &.error {\\n    background: rgba($error-color, 0.1);\\n    border-left-color: $error-color;\\n  }\\n\\n  &.success {\\n    background: rgba($success-color, 0.1);\\n    border-left-color: $success-color;\\n  }\\n}\\n\\n// 数学公式\\n.notion-equation {\\n  margin: $spacing-4 0;\\n  text-align: center;\\n  overflow-x: auto;\\n\\n  &.inline {\\n    display: inline;\\n    margin: 0;\\n  }\\n\\n  .katex {\\n    font-size: 1.1em;\\n  }\\n}\\n\\n// 表格\\n.notion-table {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: $spacing-4 0;\\n  border-radius: $border-radius-base;\\n  overflow: hidden;\\n  box-shadow: $shadow-sm;\\n\\n  th,\\n  td {\\n    padding: $spacing-3;\\n    text-align: left;\\n    border-bottom: 1px solid $border-color;\\n  }\\n\\n  th {\\n    background: $bg-secondary;\\n    font-weight: $font-weight-semibold;\\n    color: $text-primary;\\n  }\\n\\n  tr:hover {\\n    background: $bg-secondary;\\n  }\\n\\n  tr:last-child td {\\n    border-bottom: none;\\n  }\\n}\\n\\n// 切换块\\n.notion-toggle {\\n  margin: $spacing-2 0;\\n\\n  &-trigger {\\n    display: flex;\\n    align-items: center;\\n    gap: $spacing-2;\\n    cursor: pointer;\\n    padding: $spacing-2;\\n    border-radius: $border-radius-sm;\\n    transition: background-color $transition-fast;\\n\\n    &:hover {\\n      background: $bg-secondary;\\n    }\\n\\n    .toggle-icon {\\n      transition: transform $transition-fast;\\n      \\n      &.expanded {\\n        transform: rotate(90deg);\\n      }\\n    }\\n  }\\n\\n  &-content {\\n    padding-left: $spacing-6;\\n    margin-top: $spacing-2;\\n    display: none;\\n\\n    &.expanded {\\n      display: block;\\n      animation: slideDown 0.3s ease-out;\\n    }\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    opacity: 0;\\n    max-height: 0;\\n  }\\n  to {\\n    opacity: 1;\\n    max-height: 1000px;\\n  }\\n}\\n\\n// 响应式设计\\n@media (max-width: $breakpoint-md) {\\n  .notion-heading {\\n    &-1 {\\n      font-size: $font-size-2xl;\\n    }\\n\\n    &-2 {\\n      font-size: $font-size-xl;\\n    }\\n\\n    &-3 {\\n      font-size: $font-size-lg;\\n    }\\n  }\\n\\n  .notion-bookmark {\\n    flex-direction: column;\\n\\n    &-image {\\n      flex: none;\\n      height: 200px;\\n    }\\n  }\\n\\n  .notion-table {\\n    font-size: $font-size-sm;\\n\\n    th,\\n    td {\\n      padding: $spacing-2;\\n    }\\n  }\\n}\\n\",\"/**\\n * SCSS变量定义\\n */\\n\\n// 颜色系统\\n$primary-color: #0073aa;\\n$secondary-color: #005177;\\n$accent-color: #00a0d2;\\n$success-color: #46b450;\\n$warning-color: #ffb900;\\n$error-color: #dc3232;\\n$info-color: #00a0d2;\\n\\n// 灰度色彩\\n$white: #ffffff;\\n$gray-50: #f9f9f9;\\n$gray-100: #f1f1f1;\\n$gray-200: #e1e1e1;\\n$gray-300: #d1d1d1;\\n$gray-400: #c1c1c1;\\n$gray-500: #a1a1a1;\\n$gray-600: #818181;\\n$gray-700: #616161;\\n$gray-800: #414141;\\n$gray-900: #212121;\\n$black: #000000;\\n\\n// 文本颜色\\n$text-primary: $gray-900;\\n$text-secondary: $gray-700;\\n$text-muted: $gray-500;\\n$text-light: $gray-400;\\n\\n// 背景颜色\\n$bg-primary: $white;\\n$bg-secondary: $gray-50;\\n$bg-tertiary: $gray-100;\\n$bg-dark: $gray-800;\\n\\n// 边框颜色\\n$border-color: $gray-200;\\n$border-color-light: $gray-100;\\n$border-color-dark: $gray-300;\\n\\n// 字体系统\\n$font-family-base: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \\\"Helvetica Neue\\\", sans-serif;\\n$font-family-mono: Consolas, Monaco, \\\"Courier New\\\", monospace;\\n\\n// 字体大小\\n$font-size-xs: 0.75rem;    // 12px\\n$font-size-sm: 0.875rem;   // 14px\\n$font-size-base: 1rem;     // 16px\\n$font-size-lg: 1.125rem;   // 18px\\n$font-size-xl: 1.25rem;    // 20px\\n$font-size-2xl: 1.5rem;    // 24px\\n$font-size-3xl: 1.875rem;  // 30px\\n$font-size-4xl: 2.25rem;   // 36px\\n\\n// 字体权重\\n$font-weight-light: 300;\\n$font-weight-normal: 400;\\n$font-weight-medium: 500;\\n$font-weight-semibold: 600;\\n$font-weight-bold: 700;\\n\\n// 行高\\n$line-height-tight: 1.25;\\n$line-height-normal: 1.5;\\n$line-height-relaxed: 1.75;\\n\\n// 间距系统\\n$spacing-0: 0;\\n$spacing-1: 0.25rem;   // 4px\\n$spacing-2: 0.5rem;    // 8px\\n$spacing-3: 0.75rem;   // 12px\\n$spacing-4: 1rem;      // 16px\\n$spacing-5: 1.25rem;   // 20px\\n$spacing-6: 1.5rem;    // 24px\\n$spacing-8: 2rem;      // 32px\\n$spacing-10: 2.5rem;   // 40px\\n$spacing-12: 3rem;     // 48px\\n$spacing-16: 4rem;     // 64px\\n$spacing-20: 5rem;     // 80px\\n\\n// 圆角\\n$border-radius-none: 0;\\n$border-radius-sm: 0.125rem;   // 2px\\n$border-radius-base: 0.25rem;  // 4px\\n$border-radius-md: 0.375rem;   // 6px\\n$border-radius-lg: 0.5rem;     // 8px\\n$border-radius-xl: 0.75rem;    // 12px\\n$border-radius-2xl: 1rem;      // 16px\\n$border-radius-full: 9999px;\\n\\n// 阴影\\n$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n$shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n\\n// 断点\\n$breakpoint-sm: 640px;\\n$breakpoint-md: 768px;\\n$breakpoint-lg: 1024px;\\n$breakpoint-xl: 1280px;\\n$breakpoint-2xl: 1536px;\\n\\n// WordPress特定颜色\\n$wp-blue: #0073aa;\\n$wp-blue-dark: #005177;\\n$wp-green: #46b450;\\n$wp-red: #dc3232;\\n$wp-orange: #ffb900;\\n$wp-gray: #646970;\\n\\n// 组件特定变量\\n$header-height: 60px;\\n$sidebar-width: 250px;\\n$content-max-width: 1200px;\\n\\n// 动画\\n$transition-fast: 0.15s ease-in-out;\\n$transition-base: 0.3s ease-in-out;\\n$transition-slow: 0.5s ease-in-out;\\n\\n// Z-index层级\\n$z-index-dropdown: 1000;\\n$z-index-sticky: 1020;\\n$z-index-fixed: 1030;\\n$z-index-modal-backdrop: 1040;\\n$z-index-modal: 1050;\\n$z-index-popover: 1060;\\n$z-index-tooltip: 1070;\\n\\n// 表单控件\\n$input-height: 40px;\\n$input-padding-x: $spacing-3;\\n$input-padding-y: $spacing-2;\\n$input-border-width: 1px;\\n$input-border-color: $border-color;\\n$input-border-radius: $border-radius-base;\\n$input-focus-border-color: $primary-color;\\n$input-focus-box-shadow: 0 0 0 2px rgba($primary-color, 0.2);\\n\\n// 按钮\\n$button-height: 40px;\\n$button-padding-x: $spacing-4;\\n$button-padding-y: $spacing-2;\\n$button-border-radius: $border-radius-base;\\n$button-font-weight: $font-weight-medium;\\n\\n// 卡片\\n$card-padding: $spacing-6;\\n$card-border-radius: $border-radius-lg;\\n$card-border-color: $border-color;\\n$card-shadow: $shadow-sm;\\n\\n// 通知\\n$notification-padding: $spacing-4;\\n$notification-border-radius: $border-radius-base;\\n$notification-border-width: 1px;\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[2].use[3]!./src/styles/frontend/frontend.scss\n\n}");

/***/ }),

/***/ "./src/frontend/FrontendContent.ts":
/*!*****************************************!*\
  !*** ./src/frontend/FrontendContent.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrontendContent: () => (/* binding */ FrontendContent),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   frontendContent: () => (/* binding */ frontendContent)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ \"./node_modules/core-js/modules/es.function.name.js\");\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\n/* harmony import */ var _components_AnchorNavigation__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./components/AnchorNavigation */ \"./src/frontend/components/AnchorNavigation.ts\");\n/* harmony import */ var _components_LazyLoader__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/LazyLoader */ \"./src/frontend/components/LazyLoader.ts\");\n/* harmony import */ var _components_ProgressiveLoader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/ProgressiveLoader */ \"./src/frontend/components/ProgressiveLoader.ts\");\n/* harmony import */ var _components_ResourceOptimizer__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/ResourceOptimizer */ \"./src/frontend/components/ResourceOptimizer.ts\");\n\n\n\n\n\n\n\n\n\n\nvar _FrontendContent;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n\n\n\n\n\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 前端内容渲染系统 - 现代化TypeScript版本\n * \n * 完全替代原有的前端内容处理JavaScript文件，包括：\n * - 所有前端组件的统一初始化和管理\n * - 全局事件处理和协调\n * - 性能优化和用户体验增强\n * - 向后兼容性支持\n */\n\n\n\n\n\n\n\n/**\n * 前端内容渲染系统主类\n */\nvar FrontendContent = /*#__PURE__*/function () {\n  function FrontendContent() {\n    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, FrontendContent);\n    _defineProperty(this, \"initialized\", false);\n    _defineProperty(this, \"config\", void 0);\n    // 组件实例\n    _defineProperty(this, \"anchorNavigation\", void 0);\n    _defineProperty(this, \"lazyLoader\", void 0);\n    _defineProperty(this, \"progressiveLoader\", void 0);\n    _defineProperty(this, \"resourceOptimizer\", void 0);\n    if (FrontendContent.instance) {\n      return FrontendContent.instance;\n    }\n    FrontendContent.instance = this;\n    this.config = _objectSpread({\n      enableAnchorNavigation: true,\n      enableLazyLoading: true,\n      enableProgressiveLoading: true,\n      enableResourceOptimization: true,\n      enablePerformanceMonitoring: true\n    }, config);\n\n    // 初始化组件实例\n    this.anchorNavigation = _components_AnchorNavigation__WEBPACK_IMPORTED_MODULE_18__.anchorNavigation;\n    this.lazyLoader = _components_LazyLoader__WEBPACK_IMPORTED_MODULE_19__.lazyLoader;\n    this.progressiveLoader = _components_ProgressiveLoader__WEBPACK_IMPORTED_MODULE_20__.progressiveLoader;\n    this.resourceOptimizer = _components_ResourceOptimizer__WEBPACK_IMPORTED_MODULE_21__.resourceOptimizer;\n  }\n\n  /**\n   * 获取单例实例\n   */\n  return _createClass(FrontendContent, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化前端内容渲染系统\n     */\n    function init() {\n      if (this.initialized) {\n        console.warn('⚠️ [前端内容] 已经初始化，跳过重复初始化');\n        return;\n      }\n      console.log('🚀 [前端内容] 开始初始化...');\n      try {\n        this.setupGlobalEventHandlers();\n        this.setupComponentCoordination();\n        this.setupPerformanceMonitoring();\n        this.setupCompatibilityLayer();\n        this.applyConfiguration();\n        this.initialized = true;\n        (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:content:initialized');\n        console.log('✅ [前端内容] 初始化完成');\n      } catch (error) {\n        console.error('❌ [前端内容] 初始化失败:', error);\n        throw error;\n      }\n    }\n\n    /**\n     * 设置全局事件处理器\n     */\n  }, {\n    key: \"setupGlobalEventHandlers\",\n    value: function setupGlobalEventHandlers() {\n      var _this = this;\n      // 页面卸载前的清理\n      window.addEventListener('beforeunload', function () {\n        _this.cleanup();\n      });\n\n      // 页面可见性变化处理\n      document.addEventListener('visibilitychange', function () {\n        if (document.hidden) {\n          (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:page:hidden');\n        } else {\n          (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:page:visible');\n        }\n      });\n\n      // DOM变化监听（用于动态内容）\n      if ('MutationObserver' in window) {\n        var observer = new MutationObserver(function (mutations) {\n          var hasNewContent = false;\n          mutations.forEach(function (mutation) {\n            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {\n              mutation.addedNodes.forEach(function (node) {\n                if (node.nodeType === Node.ELEMENT_NODE) {\n                  var element = node;\n                  if (element.querySelector && (element.querySelector('img[data-src]') || element.querySelector('[id^=\"notion-block-\"]') || element.querySelector('.notion-progressive-loading'))) {\n                    hasNewContent = true;\n                  }\n                }\n              });\n            }\n          });\n          if (hasNewContent) {\n            _this.handleDynamicContent();\n          }\n        });\n        observer.observe(document.body, {\n          childList: true,\n          subtree: true\n        });\n      }\n      console.log('🎯 [全局事件] 已设置');\n    }\n\n    /**\n     * 设置组件协调\n     */\n  }, {\n    key: \"setupComponentCoordination\",\n    value: function setupComponentCoordination() {\n      var _this2 = this;\n      // 懒加载完成后刷新锚点导航\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('lazy:image:loaded', function () {\n        _this2.anchorNavigation.updateHeaderOffset();\n      });\n\n      // 渐进式加载完成后刷新懒加载\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('progressive:load:success', function () {\n        _this2.lazyLoader.refresh();\n      });\n\n      // 锚点导航时暂停资源优化\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('anchor:scrolled', function () {\n        // 可以在这里暂停预测性加载等\n      });\n      console.log('🔗 [组件协调] 已设置');\n    }\n\n    /**\n     * 设置性能监控\n     */\n  }, {\n    key: \"setupPerformanceMonitoring\",\n    value: function setupPerformanceMonitoring() {\n      var _this3 = this;\n      if (!this.config.enablePerformanceMonitoring) return;\n\n      // 监控页面加载性能\n      window.addEventListener('load', function () {\n        setTimeout(function () {\n          _this3.reportPagePerformance();\n        }, 1000);\n      });\n\n      // 监控组件性能\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('anchor:scrolled', function (_event, data) {\n        _this3.trackComponentPerformance('anchor_navigation', data);\n      });\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('lazy:image:loaded', function (_event, data) {\n        _this3.trackComponentPerformance('lazy_loading', data);\n      });\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.on)('progressive:load:success', function (_event, data) {\n        _this3.trackComponentPerformance('progressive_loading', data);\n      });\n      console.log('📊 [性能监控] 已设置');\n    }\n\n    /**\n     * 设置兼容性层\n     */\n  }, {\n    key: \"setupCompatibilityLayer\",\n    value: function setupCompatibilityLayer() {\n      var _this4 = this;\n      // 为了向后兼容，在全局对象上暴露一些功能\n      var globalNotionWp = window.notionToWp || {};\n\n      // 暴露组件实例\n      globalNotionWp.frontend = {\n        anchorNavigation: this.anchorNavigation,\n        lazyLoader: this.lazyLoader,\n        progressiveLoader: this.progressiveLoader,\n        resourceOptimizer: this.resourceOptimizer\n      };\n\n      // 暴露主实例\n      globalNotionWp.frontendContent = this;\n\n      // 暴露常用方法\n      globalNotionWp.scrollToAnchor = function (targetId) {\n        return _this4.anchorNavigation.scrollToAnchor(targetId);\n      };\n      globalNotionWp.refreshLazyLoading = function () {\n        _this4.lazyLoader.refresh();\n      };\n      window.notionToWp = globalNotionWp;\n      console.log('🔄 [兼容性层] 已设置');\n    }\n\n    /**\n     * 应用配置\n     */\n  }, {\n    key: \"applyConfiguration\",\n    value: function applyConfiguration() {\n      // 根据配置启用/禁用功能\n      if (!this.config.enableAnchorNavigation) {\n        // 可以在这里禁用锚点导航\n      }\n      if (!this.config.enableLazyLoading) {\n        // 可以在这里禁用懒加载\n      }\n      if (!this.config.enableProgressiveLoading) {\n        // 可以在这里禁用渐进式加载\n      }\n      if (!this.config.enableResourceOptimization) {\n        // 可以在这里禁用资源优化\n      }\n      console.log('⚙️ [配置应用] 完成:', this.config);\n    }\n\n    /**\n     * 处理动态内容\n     */\n  }, {\n    key: \"handleDynamicContent\",\n    value: function handleDynamicContent() {\n      console.log('🔄 [动态内容] 检测到新内容，刷新组件...');\n\n      // 刷新懒加载\n      this.lazyLoader.refresh();\n\n      // 更新锚点导航\n      this.anchorNavigation.updateHeaderOffset();\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:dynamic:content:detected');\n    }\n\n    /**\n     * 报告页面性能\n     */\n  }, {\n    key: \"reportPagePerformance\",\n    value: function reportPagePerformance() {\n      if (!('performance' in window)) return;\n      var navigation = performance.getEntriesByType('navigation')[0];\n      if (!navigation) return;\n      var metrics = {\n        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,\n        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,\n        firstPaint: 0,\n        firstContentfulPaint: 0\n      };\n\n      // 获取绘制指标\n      var paintEntries = performance.getEntriesByType('paint');\n      paintEntries.forEach(function (entry) {\n        if (entry.name === 'first-paint') {\n          metrics.firstPaint = entry.startTime;\n        } else if (entry.name === 'first-contentful-paint') {\n          metrics.firstContentfulPaint = entry.startTime;\n        }\n      });\n      console.log('📊 [页面性能]', metrics);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:performance:report', metrics);\n    }\n\n    /**\n     * 追踪组件性能\n     */\n  }, {\n    key: \"trackComponentPerformance\",\n    value: function trackComponentPerformance(component, data) {\n      var timestamp = Date.now();\n      console.log(\"\\uD83D\\uDCCA [\\u7EC4\\u4EF6\\u6027\\u80FD] \".concat(component, \":\"), data);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:component:performance', {\n        component: component,\n        data: data,\n        timestamp: timestamp\n      });\n    }\n\n    /**\n     * 获取组件实例\n     */\n  }, {\n    key: \"getAnchorNavigation\",\n    value: function getAnchorNavigation() {\n      return this.anchorNavigation;\n    }\n  }, {\n    key: \"getLazyLoader\",\n    value: function getLazyLoader() {\n      return this.lazyLoader;\n    }\n  }, {\n    key: \"getProgressiveLoader\",\n    value: function getProgressiveLoader() {\n      return this.progressiveLoader;\n    }\n  }, {\n    key: \"getResourceOptimizer\",\n    value: function getResourceOptimizer() {\n      return this.resourceOptimizer;\n    }\n\n    /**\n     * 获取配置\n     */\n  }, {\n    key: \"getConfig\",\n    value: function getConfig() {\n      return _objectSpread({}, this.config);\n    }\n\n    /**\n     * 更新配置\n     */\n  }, {\n    key: \"updateConfig\",\n    value: function updateConfig(newConfig) {\n      this.config = _objectSpread(_objectSpread({}, this.config), newConfig);\n      this.applyConfiguration();\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:config:updated', this.config);\n    }\n\n    /**\n     * 检查是否已初始化\n     */\n  }, {\n    key: \"isInitialized\",\n    value: function isInitialized() {\n      return this.initialized;\n    }\n\n    /**\n     * 获取系统状态\n     */\n  }, {\n    key: \"getSystemStatus\",\n    value: function getSystemStatus() {\n      return {\n        initialized: this.initialized,\n        componentsActive: {\n          anchorNavigation: true,\n          // AnchorNavigation 总是活跃的\n          lazyLoader: this.lazyLoader.isObserverSupported(),\n          progressiveLoader: true,\n          // ProgressiveLoader 总是活跃的\n          resourceOptimizer: true // ResourceOptimizer 总是活跃的\n        },\n        config: this.config\n      };\n    }\n\n    /**\n     * 清理资源\n     */\n  }, {\n    key: \"cleanup\",\n    value: function cleanup() {\n      if (!this.initialized) return;\n      console.log('🧹 [前端内容] 开始清理...');\n      try {\n        this.anchorNavigation.destroy();\n        this.lazyLoader.destroy();\n        this.progressiveLoader.destroy();\n        this.resourceOptimizer.destroy();\n        this.initialized = false;\n        FrontendContent.instance = null;\n        (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_16__.emit)('frontend:content:destroyed');\n        console.log('✅ [前端内容] 清理完成');\n      } catch (error) {\n        console.error('❌ [前端内容] 清理失败:', error);\n      }\n    }\n\n    /**\n     * 销毁实例\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.cleanup();\n    }\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance(config) {\n      if (!FrontendContent.instance) {\n        FrontendContent.instance = new FrontendContent(config);\n      }\n      return FrontendContent.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_FrontendContent = FrontendContent;\n_defineProperty(FrontendContent, \"instance\", null);\nvar frontendContent = FrontendContent.getInstance();\n\n// 自动初始化\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_17__.ready)(function () {\n  frontendContent.init();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FrontendContent);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/FrontendContent.ts\n\n}");

/***/ }),

/***/ "./src/frontend/components/AnchorNavigation.ts":
/*!*****************************************************!*\
  !*** ./src/frontend/components/AnchorNavigation.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnchorNavigation: () => (/* binding */ AnchorNavigation),\n/* harmony export */   anchorNavigation: () => (/* binding */ anchorNavigation),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ \"./node_modules/core-js/modules/es.regexp.exec.js\");\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ \"./node_modules/core-js/modules/es.string.replace.js\");\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\n\n\n\n\n\n\n\n\n\n\nvar _AnchorNavigation;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n\n\n\n\n\n\n\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 锚点导航系统 - 现代化TypeScript版本\n * \n * 从原有anchor-navigation.js完全迁移，包括：\n * - 平滑滚动到Notion区块锚点\n * - 固定头部偏移处理\n * - 区块高亮效果\n * - URL状态管理\n */\n\n\n\n/**\n * 锚点导航系统类\n */\nvar AnchorNavigation = /*#__PURE__*/function () {\n  function AnchorNavigation() {\n    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, AnchorNavigation);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"headerOffset\", 0);\n    _defineProperty(this, \"supportsSmoothScroll\", void 0);\n    _defineProperty(this, \"resizeObserver\", null);\n    if (AnchorNavigation.instance) {\n      return AnchorNavigation.instance;\n    }\n    AnchorNavigation.instance = this;\n    this.config = _objectSpread({\n      headerSelectors: ['header[style*=\"position: fixed\"]', '.fixed-header', '.sticky-header', '#masthead', '.site-header'],\n      smoothScrollSupported: 'scrollBehavior' in document.documentElement.style,\n      highlightDuration: 2000,\n      scrollOffset: 20\n    }, config);\n    this.supportsSmoothScroll = this.config.smoothScrollSupported;\n    this.init();\n  }\n\n  /**\n   * 获取单例实例\n   */\n  return _createClass(AnchorNavigation, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化锚点导航系统\n     */\n    function init() {\n      this.updateHeaderOffset();\n      this.setupEventListeners();\n      this.handleInitialHash();\n      console.log('🔗 [锚点导航] 已初始化');\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:navigation:initialized');\n    }\n\n    /**\n     * 设置事件监听器\n     */\n  }, {\n    key: \"setupEventListeners\",\n    value: function setupEventListeners() {\n      // 点击事件委托\n      document.addEventListener('click', this.handleAnchorClick.bind(this));\n\n      // Hash变化监听\n      window.addEventListener('hashchange', this.debounce(this.handleHashChange.bind(this), 100));\n\n      // 窗口大小变化监听\n      window.addEventListener('resize', this.debounce(this.updateHeaderOffset.bind(this), 250));\n\n      // 使用ResizeObserver监听头部元素变化\n      if ('ResizeObserver' in window) {\n        this.setupHeaderObserver();\n      }\n    }\n\n    /**\n     * 设置头部观察器\n     */\n  }, {\n    key: \"setupHeaderObserver\",\n    value: function setupHeaderObserver() {\n      var _this = this;\n      this.resizeObserver = new ResizeObserver(this.debounce(function () {\n        _this.updateHeaderOffset();\n      }, 100));\n\n      // 观察所有可能的头部元素\n      this.config.headerSelectors.forEach(function (selector) {\n        var elements = document.querySelectorAll(selector);\n        elements.forEach(function (element) {\n          if (element instanceof HTMLElement) {\n            _this.resizeObserver.observe(element);\n          }\n        });\n      });\n    }\n\n    /**\n     * 检测固定头部高度\n     */\n  }, {\n    key: \"calculateHeaderOffset\",\n    value: function calculateHeaderOffset() {\n      var maxHeight = 0;\n      this.config.headerSelectors.forEach(function (selector) {\n        var element = document.querySelector(selector);\n        if (element) {\n          var style = window.getComputedStyle(element);\n          if (style.position === 'fixed' || style.position === 'sticky') {\n            maxHeight = Math.max(maxHeight, element.offsetHeight);\n          }\n        }\n      });\n      return maxHeight + this.config.scrollOffset;\n    }\n\n    /**\n     * 更新头部偏移\n     */\n  }, {\n    key: \"updateHeaderOffset\",\n    value: function updateHeaderOffset() {\n      var newOffset = this.calculateHeaderOffset();\n      if (newOffset !== this.headerOffset) {\n        this.headerOffset = newOffset;\n        document.documentElement.style.setProperty('--ntw-header-offset', \"\".concat(this.headerOffset, \"px\"));\n        (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:header:offset:updated', {\n          offset: this.headerOffset\n        });\n        console.log(\"\\uD83D\\uDD17 [\\u951A\\u70B9\\u5BFC\\u822A] \\u5934\\u90E8\\u504F\\u79FB\\u5DF2\\u66F4\\u65B0: \".concat(this.headerOffset, \"px\"));\n      }\n    }\n\n    /**\n     * 平滑滚动到锚点\n     */\n  }, {\n    key: \"scrollToAnchor\",\n    value: function scrollToAnchor(targetId) {\n      if (!targetId || !targetId.startsWith('#notion-block-')) {\n        return false;\n      }\n      var cleanId = targetId.replace('#', '');\n      var target = document.getElementById(cleanId);\n      if (!target) {\n        console.warn(\"\\uD83D\\uDD17 [\\u951A\\u70B9\\u5BFC\\u822A] \\u672A\\u627E\\u5230\\u76EE\\u6807\\u5143\\u7D20: \".concat(targetId));\n        return false;\n      }\n      var scrollTarget = {\n        id: cleanId,\n        element: target,\n        rect: target.getBoundingClientRect()\n      };\n\n      // 执行滚动\n      this.performScroll(scrollTarget);\n\n      // 高亮效果\n      this.highlightBlock(target);\n\n      // 更新URL\n      this.updateURL(targetId);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:scrolled', scrollTarget);\n      console.log(\"\\uD83D\\uDD17 [\\u951A\\u70B9\\u5BFC\\u822A] \\u6EDA\\u52A8\\u5230: \".concat(targetId));\n      return true;\n    }\n\n    /**\n     * 执行滚动操作\n     */\n  }, {\n    key: \"performScroll\",\n    value: function performScroll(scrollTarget) {\n      var _this2 = this;\n      var element = scrollTarget.element;\n\n      // 首先滚动到元素中心\n      var scrollOptions = {\n        block: 'center',\n        behavior: this.supportsSmoothScroll ? 'smooth' : 'auto'\n      };\n      element.scrollIntoView(scrollOptions);\n\n      // 调整头部偏移\n      setTimeout(function () {\n        var rect = element.getBoundingClientRect();\n        if (rect.top < _this2.headerOffset) {\n          var offset = rect.top - _this2.headerOffset;\n          if (_this2.supportsSmoothScroll) {\n            window.scrollBy({\n              top: offset,\n              behavior: 'smooth'\n            });\n          } else {\n            window.scrollBy(0, offset);\n          }\n        }\n      }, this.supportsSmoothScroll ? 100 : 0);\n    }\n\n    /**\n     * 高亮区块\n     */\n  }, {\n    key: \"highlightBlock\",\n    value: function highlightBlock(element) {\n      if (!element || !element.classList) return;\n\n      // 移除现有高亮\n      element.classList.remove('notion-block-highlight');\n\n      // 强制重绘\n      element.offsetWidth;\n\n      // 添加高亮\n      element.classList.add('notion-block-highlight');\n\n      // 监听动画结束事件\n      var _removeHighlight = function removeHighlight() {\n        element.classList.remove('notion-block-highlight');\n        element.removeEventListener('animationend', _removeHighlight);\n      };\n      element.addEventListener('animationend', _removeHighlight, {\n        once: true\n      });\n\n      // 备用定时器（防止动画事件不触发）\n      setTimeout(function () {\n        element.classList.remove('notion-block-highlight');\n      }, this.config.highlightDuration);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:block:highlighted', {\n        element: element,\n        id: element.id\n      });\n    }\n\n    /**\n     * 更新URL\n     */\n  }, {\n    key: \"updateURL\",\n    value: function updateURL(targetId) {\n      if (window.history && window.history.replaceState) {\n        try {\n          window.history.replaceState(null, '', targetId);\n          (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:url:updated', {\n            hash: targetId\n          });\n        } catch (error) {\n          console.warn('🔗 [锚点导航] URL更新失败:', error);\n        }\n      }\n    }\n\n    /**\n     * 处理锚点点击\n     */\n  }, {\n    key: \"handleAnchorClick\",\n    value: function handleAnchorClick(event) {\n      var target = event.target;\n      var link = target.closest('a[href^=\"#notion-block-\"]');\n      if (link) {\n        event.preventDefault();\n        var href = link.getAttribute('href');\n        if (href) {\n          this.scrollToAnchor(href);\n        }\n      }\n    }\n\n    /**\n     * 处理hash变化\n     */\n  }, {\n    key: \"handleHashChange\",\n    value: function handleHashChange() {\n      var hash = window.location.hash;\n      if (hash && hash.startsWith('#notion-block-')) {\n        this.scrollToAnchor(hash);\n      }\n    }\n\n    /**\n     * 处理初始hash\n     */\n  }, {\n    key: \"handleInitialHash\",\n    value: function handleInitialHash() {\n      var _this3 = this;\n      var hash = window.location.hash;\n      if (hash && hash.startsWith('#notion-block-')) {\n        // 延迟处理，确保页面完全加载\n        setTimeout(function () {\n          _this3.scrollToAnchor(hash);\n        }, 500);\n      }\n    }\n\n    /**\n     * 防抖函数\n     */\n  }, {\n    key: \"debounce\",\n    value: function debounce(func, wait) {\n      var _this4 = this;\n      var timeout;\n      return function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(function () {\n          return func.apply(_this4, args);\n        }, wait);\n      };\n    }\n\n    /**\n     * 获取当前配置\n     */\n  }, {\n    key: \"getConfig\",\n    value: function getConfig() {\n      return _objectSpread({}, this.config);\n    }\n\n    /**\n     * 更新配置\n     */\n  }, {\n    key: \"updateConfig\",\n    value: function updateConfig(newConfig) {\n      this.config = _objectSpread(_objectSpread({}, this.config), newConfig);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:config:updated', this.config);\n    }\n\n    /**\n     * 获取头部偏移\n     */\n  }, {\n    key: \"getHeaderOffset\",\n    value: function getHeaderOffset() {\n      return this.headerOffset;\n    }\n\n    /**\n     * 获取所有可滚动的锚点\n     */\n  }, {\n    key: \"getAllAnchors\",\n    value: function getAllAnchors() {\n      var anchors = [];\n      var elements = document.querySelectorAll('[id^=\"notion-block-\"]');\n      elements.forEach(function (element) {\n        if (element instanceof HTMLElement) {\n          anchors.push({\n            id: element.id,\n            element: element,\n            rect: element.getBoundingClientRect()\n          });\n        }\n      });\n      return anchors;\n    }\n\n    /**\n     * 销毁实例\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      // 移除事件监听器\n      document.removeEventListener('click', this.handleAnchorClick);\n      window.removeEventListener('hashchange', this.handleHashChange);\n      window.removeEventListener('resize', this.updateHeaderOffset);\n\n      // 清理ResizeObserver\n      if (this.resizeObserver) {\n        this.resizeObserver.disconnect();\n        this.resizeObserver = null;\n      }\n\n      // 清理CSS变量\n      document.documentElement.style.removeProperty('--ntw-header-offset');\n      AnchorNavigation.instance = null;\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_18__.emit)('anchor:navigation:destroyed');\n      console.log('🔗 [锚点导航] 已销毁');\n    }\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance(config) {\n      if (!AnchorNavigation.instance) {\n        AnchorNavigation.instance = new AnchorNavigation(config);\n      }\n      return AnchorNavigation.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_AnchorNavigation = AnchorNavigation;\n_defineProperty(AnchorNavigation, \"instance\", null);\nvar anchorNavigation = AnchorNavigation.getInstance();\n\n// 自动初始化\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_19__.ready)(function () {\n  anchorNavigation;\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnchorNavigation);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/AnchorNavigation.ts\n\n}");

/***/ }),

/***/ "./src/frontend/components/LazyLoader.ts":
/*!***********************************************!*\
  !*** ./src/frontend/components/LazyLoader.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LazyLoader: () => (/* binding */ LazyLoader),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   lazyLoader: () => (/* binding */ lazyLoader)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ \"./node_modules/core-js/modules/es.array.concat.js\");\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ \"./node_modules/core-js/modules/es.array.map.js\");\n/* harmony import */ var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.map.js */ \"./node_modules/core-js/modules/es.map.js\");\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.set.js */ \"./node_modules/core-js/modules/es.set.js\");\n/* harmony import */ var core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var core_js_modules_esnext_promise_all_settled_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core-js/modules/esnext.promise.all-settled.js */ \"./node_modules/core-js/modules/esnext.promise.all-settled.js\");\n/* harmony import */ var core_js_modules_esnext_promise_all_settled_js__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_esnext_promise_all_settled_js__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\nvar _LazyLoader;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 懒加载系统 - 现代化TypeScript版本\n * \n * 从原有lazy-loading.js完全迁移，包括：\n * - Intersection Observer API图片懒加载\n * - 渐进式内容加载\n * - 外部特色图像处理\n * - 错误处理和降级支持\n */\n\n\n\n/**\n * 懒加载系统类\n */\nvar LazyLoader = /*#__PURE__*/function () {\n  function LazyLoader() {\n    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, LazyLoader);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"observer\", null);\n    _defineProperty(this, \"supportsIntersectionObserver\", void 0);\n    _defineProperty(this, \"loadedImages\", new Set());\n    _defineProperty(this, \"errorImages\", new Set());\n    _defineProperty(this, \"retryQueue\", new Map());\n    if (LazyLoader.instance) {\n      return LazyLoader.instance;\n    }\n    LazyLoader.instance = this;\n    this.config = _objectSpread({\n      rootMargin: '50px 0px',\n      threshold: 0.1,\n      loadingClass: 'notion-lazy-loading',\n      loadedClass: 'notion-lazy-loaded',\n      errorClass: 'notion-lazy-error',\n      observedClass: 'notion-lazy-observed',\n      retryAttempts: 3,\n      retryDelay: 1000\n    }, config);\n    this.supportsIntersectionObserver = 'IntersectionObserver' in window;\n    this.init();\n  }\n\n  /**\n   * 获取单例实例\n   */\n  return _createClass(LazyLoader, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化懒加载系统\n     */\n    function init() {\n      if (this.supportsIntersectionObserver) {\n        this.createObserver();\n        this.observeImages();\n      } else {\n        this.fallbackLoad();\n      }\n      console.log(\"\\uD83D\\uDDBC\\uFE0F [\\u61D2\\u52A0\\u8F7D] \\u5DF2\\u521D\\u59CB\\u5316 (\".concat(this.supportsIntersectionObserver ? 'Observer模式' : '降级模式', \")\"));\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:loader:initialized', {\n        observerSupported: this.supportsIntersectionObserver\n      });\n    }\n\n    /**\n     * 创建Intersection Observer\n     */\n  }, {\n    key: \"createObserver\",\n    value: function createObserver() {\n      var _this = this;\n      this.observer = new IntersectionObserver(function (entries) {\n        entries.forEach(function (entry) {\n          if (entry.isIntersecting) {\n            var img = entry.target;\n            _this.loadImage(img);\n            _this.observer.unobserve(img);\n          }\n        });\n      }, {\n        rootMargin: this.config.rootMargin,\n        threshold: this.config.threshold\n      });\n    }\n\n    /**\n     * 观察所有懒加载图片\n     */\n  }, {\n    key: \"observeImages\",\n    value: function observeImages() {\n      var _this2 = this;\n      var lazyImages = document.querySelectorAll('img[data-src]:not(.notion-lazy-observed)');\n      lazyImages.forEach(function (img) {\n        if (img instanceof HTMLImageElement) {\n          img.classList.add(_this2.config.observedClass);\n          _this2.observer.observe(img);\n        }\n      });\n      if (lazyImages.length > 0) {\n        console.log(\"\\uD83D\\uDDBC\\uFE0F [\\u61D2\\u52A0\\u8F7D] \\u89C2\\u5BDF\\u56FE\\u7247\\u6570\\u91CF: \".concat(lazyImages.length));\n      }\n    }\n\n    /**\n     * 加载图片\n     */\n  }, {\n    key: \"loadImage\",\n    value: (function () {\n      var _loadImage = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(img) {\n        var src, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              src = img.dataset.src;\n              if (src) {\n                _context.n = 1;\n                break;\n              }\n              return _context.a(2);\n            case 1:\n              // 添加加载状态\n              img.classList.add(this.config.loadingClass);\n              img._lazyOriginalSrc = src;\n              _context.p = 2;\n              _context.n = 3;\n              return this.preloadImage(src);\n            case 3:\n              // 加载成功\n              img.src = src;\n              img.classList.remove(this.config.loadingClass);\n              img.classList.add(this.config.loadedClass);\n              this.loadedImages.add(src);\n\n              // 清理数据属性\n              delete img.dataset.src;\n\n              // 触发自定义事件\n              img.dispatchEvent(new CustomEvent('lazyLoaded', {\n                detail: {\n                  src: src,\n                  element: img\n                }\n              }));\n              (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:image:loaded', {\n                src: src,\n                element: img\n              });\n              _context.n = 5;\n              break;\n            case 4:\n              _context.p = 4;\n              _t = _context.v;\n              _context.n = 5;\n              return this.handleImageError(img, _t);\n            case 5:\n              return _context.a(2);\n          }\n        }, _callee, this, [[2, 4]]);\n      }));\n      function loadImage(_x) {\n        return _loadImage.apply(this, arguments);\n      }\n      return loadImage;\n    }()\n    /**\n     * 预加载图片\n     */\n    )\n  }, {\n    key: \"preloadImage\",\n    value: function preloadImage(src) {\n      return new Promise(function (resolve, reject) {\n        var imageLoader = new Image();\n        imageLoader.onload = function () {\n          return resolve();\n        };\n        imageLoader.onerror = function () {\n          return reject(new Error(\"\\u56FE\\u7247\\u52A0\\u8F7D\\u5931\\u8D25: \".concat(src)));\n        };\n        imageLoader.src = src;\n      });\n    }\n\n    /**\n     * 处理图片加载错误\n     */\n  }, {\n    key: \"handleImageError\",\n    value: (function () {\n      var _handleImageError = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(img, error) {\n        var _this3 = this;\n        var src, retryCount;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              src = img._lazyOriginalSrc || img.dataset.src || '';\n              retryCount = img._lazyRetryCount || 0;\n              if (!(retryCount < this.config.retryAttempts)) {\n                _context2.n = 1;\n                break;\n              }\n              // 重试加载\n              img._lazyRetryCount = retryCount + 1;\n              console.warn(\"\\uD83D\\uDDBC\\uFE0F [\\u61D2\\u52A0\\u8F7D] \\u91CD\\u8BD5\\u52A0\\u8F7D\\u56FE\\u7247 (\".concat(retryCount + 1, \"/\").concat(this.config.retryAttempts, \"): \").concat(src));\n              setTimeout(function () {\n                _this3.loadImage(img);\n              }, this.config.retryDelay * (retryCount + 1));\n              return _context2.a(2);\n            case 1:\n              // 重试次数用完，显示错误状态\n              img.classList.remove(this.config.loadingClass);\n              img.classList.add(this.config.errorClass);\n              this.errorImages.add(src);\n\n              // 设置错误占位符\n              this.setErrorPlaceholder(img);\n\n              // 触发自定义事件\n              img.dispatchEvent(new CustomEvent('lazyError', {\n                detail: {\n                  src: src,\n                  error: error,\n                  element: img,\n                  retryCount: retryCount\n                }\n              }));\n              (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:image:error', {\n                src: src,\n                error: error,\n                element: img,\n                retryCount: retryCount\n              });\n              console.error(\"\\uD83D\\uDDBC\\uFE0F [\\u61D2\\u52A0\\u8F7D] \\u56FE\\u7247\\u52A0\\u8F7D\\u5931\\u8D25: \".concat(src), error);\n            case 2:\n              return _context2.a(2);\n          }\n        }, _callee2, this);\n      }));\n      function handleImageError(_x2, _x3) {\n        return _handleImageError.apply(this, arguments);\n      }\n      return handleImageError;\n    }()\n    /**\n     * 设置错误占位符\n     */\n    )\n  }, {\n    key: \"setErrorPlaceholder\",\n    value: function setErrorPlaceholder(img) {\n      var placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';\n      img.src = placeholder;\n    }\n\n    /**\n     * 降级处理（不支持Intersection Observer时）\n     */\n  }, {\n    key: \"fallbackLoad\",\n    value: function fallbackLoad() {\n      var _this4 = this;\n      var lazyImages = document.querySelectorAll('img[data-src]');\n      lazyImages.forEach(function (img) {\n        if (img instanceof HTMLImageElement) {\n          _this4.loadImage(img);\n        }\n      });\n      console.log(\"\\uD83D\\uDDBC\\uFE0F [\\u61D2\\u52A0\\u8F7D] \\u964D\\u7EA7\\u6A21\\u5F0F\\u52A0\\u8F7D\\u56FE\\u7247\\u6570\\u91CF: \".concat(lazyImages.length));\n    }\n\n    /**\n     * 刷新懒加载图片\n     */\n  }, {\n    key: \"refresh\",\n    value: function refresh() {\n      if (!this.supportsIntersectionObserver) return;\n      this.observeImages();\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:loader:refreshed');\n    }\n\n    /**\n     * 预加载指定图片\n     */\n  }, {\n    key: \"preloadImages\",\n    value: (function () {\n      var _preloadImages = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(urls) {\n        var _this5 = this;\n        var promises;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              if (Array.isArray(urls)) {\n                _context4.n = 1;\n                break;\n              }\n              return _context4.a(2);\n            case 1:\n              promises = urls.map(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(url) {\n                  var _t2;\n                  return _regenerator().w(function (_context3) {\n                    while (1) switch (_context3.p = _context3.n) {\n                      case 0:\n                        _context3.p = 0;\n                        _context3.n = 1;\n                        return _this5.preloadImage(url);\n                      case 1:\n                        console.log(\"\\uD83D\\uDDBC\\uFE0F [\\u9884\\u52A0\\u8F7D] \".concat(url, \" \\u5B8C\\u6210\"));\n                        _context3.n = 3;\n                        break;\n                      case 2:\n                        _context3.p = 2;\n                        _t2 = _context3.v;\n                        console.warn(\"\\uD83D\\uDDBC\\uFE0F [\\u9884\\u52A0\\u8F7D] \".concat(url, \" \\u5931\\u8D25:\"), _t2);\n                      case 3:\n                        return _context3.a(2);\n                    }\n                  }, _callee3, null, [[0, 2]]);\n                }));\n                return function (_x5) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n              _context4.n = 2;\n              return Promise.allSettled(promises);\n            case 2:\n              (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:preload:completed', {\n                urls: urls,\n                count: urls.length\n              });\n            case 3:\n              return _context4.a(2);\n          }\n        }, _callee4);\n      }));\n      function preloadImages(_x4) {\n        return _preloadImages.apply(this, arguments);\n      }\n      return preloadImages;\n    }()\n    /**\n     * 手动触发图片加载\n     */\n    )\n  }, {\n    key: \"loadImageManually\",\n    value: function loadImageManually(img) {\n      if (img.dataset.src) {\n        this.loadImage(img);\n      }\n    }\n\n    /**\n     * 获取懒加载统计信息\n     */\n  }, {\n    key: \"getStats\",\n    value: function getStats() {\n      var totalImages = document.querySelectorAll('img[data-src]').length;\n      var loadedImages = document.querySelectorAll(\".\".concat(this.config.loadedClass)).length;\n      var errorImages = document.querySelectorAll(\".\".concat(this.config.errorClass)).length;\n      return {\n        totalImages: totalImages,\n        loadedImages: loadedImages,\n        errorImages: errorImages,\n        observerSupported: this.supportsIntersectionObserver,\n        retryAttempts: this.retryQueue.size\n      };\n    }\n\n    /**\n     * 获取配置\n     */\n  }, {\n    key: \"getConfig\",\n    value: function getConfig() {\n      return _objectSpread({}, this.config);\n    }\n\n    /**\n     * 更新配置\n     */\n  }, {\n    key: \"updateConfig\",\n    value: function updateConfig(newConfig) {\n      this.config = _objectSpread(_objectSpread({}, this.config), newConfig);\n\n      // 如果Observer相关配置改变，重新创建Observer\n      if (this.observer && (newConfig.rootMargin || newConfig.threshold)) {\n        this.observer.disconnect();\n        this.createObserver();\n        this.observeImages();\n      }\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:config:updated', this.config);\n    }\n\n    /**\n     * 获取Observer实例\n     */\n  }, {\n    key: \"getObserver\",\n    value: function getObserver() {\n      return this.observer;\n    }\n\n    /**\n     * 检查是否支持Intersection Observer\n     */\n  }, {\n    key: \"isObserverSupported\",\n    value: function isObserverSupported() {\n      return this.supportsIntersectionObserver;\n    }\n\n    /**\n     * 销毁懒加载系统\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (this.observer) {\n        this.observer.disconnect();\n        this.observer = null;\n      }\n      this.loadedImages.clear();\n      this.errorImages.clear();\n      this.retryQueue.clear();\n      LazyLoader.instance = null;\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_23__.emit)('lazy:loader:destroyed');\n      console.log('🖼️ [懒加载] 已销毁');\n    }\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance(config) {\n      if (!LazyLoader.instance) {\n        LazyLoader.instance = new LazyLoader(config);\n      }\n      return LazyLoader.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_LazyLoader = LazyLoader;\n_defineProperty(LazyLoader, \"instance\", null);\nvar lazyLoader = LazyLoader.getInstance();\n\n// 自动初始化\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_24__.ready)(function () {\n  lazyLoader;\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LazyLoader);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/LazyLoader.ts\n\n}");

/***/ }),

/***/ "./src/frontend/components/ProgressiveLoader.ts":
/*!******************************************************!*\
  !*** ./src/frontend/components/ProgressiveLoader.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressiveLoader: () => (/* binding */ ProgressiveLoader),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   progressiveLoader: () => (/* binding */ progressiveLoader)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ \"./node_modules/core-js/modules/es.array.concat.js\");\n/* harmony import */ var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.join.js */ \"./node_modules/core-js/modules/es.array.join.js\");\n/* harmony import */ var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ \"./node_modules/core-js/modules/es.array.map.js\");\n/* harmony import */ var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.map.js */ \"./node_modules/core-js/modules/es.map.js\");\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.object.values.js */ \"./node_modules/core-js/modules/es.object.values.js\");\n/* harmony import */ var core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ \"./node_modules/core-js/modules/es.regexp.exec.js\");\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ \"./node_modules/core-js/modules/es.string.replace.js\");\n/* harmony import */ var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! core-js/modules/es.string.starts-with.js */ \"./node_modules/core-js/modules/es.string.starts-with.js\");\n/* harmony import */ var core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_starts_with_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _shared_utils_ajax__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ../../shared/utils/ajax */ \"./src/shared/utils/ajax.ts\");\n/* harmony import */ var _LazyLoader__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./LazyLoader */ \"./src/frontend/components/LazyLoader.ts\");\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ../../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\nvar _ProgressiveLoader;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 渐进式加载器 - 现代化TypeScript版本\n * \n * 从原有lazy-loading.js的渐进式加载功能完全迁移，包括：\n * - 数据库视图的渐进式加载\n * - 加载状态管理\n * - 内容渲染和集成\n */\n\n\n\n\n/**\n * 渐进式加载器类\n */\nvar ProgressiveLoader = /*#__PURE__*/function () {\n  function ProgressiveLoader() {\n    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, ProgressiveLoader);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"loadingStates\", new Map());\n    _defineProperty(this, \"loadedData\", new Map());\n    if (ProgressiveLoader.instance) {\n      return ProgressiveLoader.instance;\n    }\n    ProgressiveLoader.instance = this;\n    this.config = _objectSpread({\n      loadingDelay: 500,\n      retryAttempts: 3,\n      retryDelay: 1000,\n      batchSize: 10\n    }, config);\n    this.init();\n  }\n\n  /**\n   * 获取单例实例\n   */\n  return _createClass(ProgressiveLoader, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化渐进式加载器\n     */\n    function init() {\n      this.setupEventListeners();\n      console.log('📄 [渐进式加载] 已初始化');\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('progressive:loader:initialized');\n    }\n\n    /**\n     * 设置事件监听器\n     */\n  }, {\n    key: \"setupEventListeners\",\n    value: function setupEventListeners() {\n      // 使用事件委托处理加载更多按钮\n      document.addEventListener('click', this.handleLoadMoreClick.bind(this));\n    }\n\n    /**\n     * 处理加载更多按钮点击\n     */\n  }, {\n    key: \"handleLoadMoreClick\",\n    value: function handleLoadMoreClick(event) {\n      var target = event.target;\n      var button = target.closest('.notion-load-more-button');\n      if (button) {\n        event.preventDefault();\n        this.loadMore(button);\n      }\n    }\n\n    /**\n     * 加载更多内容\n     */\n  }, {\n    key: \"loadMore\",\n    value: (function () {\n      var _loadMore = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(button) {\n        var container, containerId, data, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              container = button.closest('.notion-progressive-loading');\n              if (container) {\n                _context.n = 1;\n                break;\n              }\n              console.error('📄 [渐进式加载] 未找到容器元素');\n              return _context.a(2);\n            case 1:\n              containerId = container.id || this.generateContainerId(); // 防止重复加载\n              if (!this.loadingStates.get(containerId)) {\n                _context.n = 2;\n                break;\n              }\n              return _context.a(2);\n            case 2:\n              _context.p = 2;\n              this.setLoadingState(button, true);\n              this.loadingStates.set(containerId, true);\n              _context.n = 3;\n              return this.fetchMoreData(container);\n            case 3:\n              data = _context.v;\n              if (!(data && data.records.length > 0)) {\n                _context.n = 5;\n                break;\n              }\n              _context.n = 4;\n              return this.renderRecords(container, data.records);\n            case 4:\n              // 如果没有更多数据，隐藏按钮\n              if (!data.hasMore) {\n                this.hideLoadMoreButton(button);\n              }\n\n              // 刷新懒加载\n              _LazyLoader__WEBPACK_IMPORTED_MODULE_29__.lazyLoader.refresh();\n              (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('progressive:load:success', {\n                containerId: containerId,\n                recordCount: data.records.length,\n                hasMore: data.hasMore\n              });\n              console.log(\"\\uD83D\\uDCC4 [\\u6E10\\u8FDB\\u5F0F\\u52A0\\u8F7D] \\u52A0\\u8F7D\\u5B8C\\u6210\\uFF0C\\u8BB0\\u5F55\\u6570: \".concat(data.records.length));\n              _context.n = 6;\n              break;\n            case 5:\n              this.hideLoadMoreButton(button);\n              console.log('📄 [渐进式加载] 没有更多数据');\n            case 6:\n              _context.n = 8;\n              break;\n            case 7:\n              _context.p = 7;\n              _t = _context.v;\n              this.handleLoadError(button, _t);\n              (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('progressive:load:error', {\n                containerId: containerId,\n                error: _t\n              });\n            case 8:\n              _context.p = 8;\n              this.setLoadingState(button, false);\n              this.loadingStates.set(containerId, false);\n              return _context.f(8);\n            case 9:\n              return _context.a(2);\n          }\n        }, _callee, this, [[2, 7, 8, 9]]);\n      }));\n      function loadMore(_x) {\n        return _loadMore.apply(this, arguments);\n      }\n      return loadMore;\n    }()\n    /**\n     * 获取更多数据\n     */\n    )\n  }, {\n    key: \"fetchMoreData\",\n    value: (function () {\n      var _fetchMoreData = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(container) {\n        var _this = this;\n        var recordsData, data, endpoint, params, response, _t2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.p = _context2.n) {\n            case 0:\n              recordsData = container.dataset.records;\n              if (!recordsData) {\n                _context2.n = 4;\n                break;\n              }\n              _context2.p = 1;\n              data = JSON.parse(atob(recordsData)); // 模拟API延迟\n              _context2.n = 2;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, _this.config.loadingDelay);\n              });\n            case 2:\n              return _context2.a(2, data);\n            case 3:\n              _context2.p = 3;\n              _t2 = _context2.v;\n              throw new Error('数据解析失败');\n            case 4:\n              // 从API获取数据（动态数据）\n              endpoint = container.dataset.endpoint;\n              if (endpoint) {\n                _context2.n = 5;\n                break;\n              }\n              throw new Error('未配置数据端点');\n            case 5:\n              params = this.getLoadParams(container);\n              _context2.n = 6;\n              return (0,_shared_utils_ajax__WEBPACK_IMPORTED_MODULE_28__.post)(endpoint, params);\n            case 6:\n              response = _context2.v;\n              if (!response.data.success) {\n                _context2.n = 7;\n                break;\n              }\n              return _context2.a(2, response.data.data);\n            case 7:\n              throw new Error(response.data.message || '数据获取失败');\n            case 8:\n              return _context2.a(2);\n          }\n        }, _callee2, this, [[1, 3]]);\n      }));\n      function fetchMoreData(_x2) {\n        return _fetchMoreData.apply(this, arguments);\n      }\n      return fetchMoreData;\n    }()\n    /**\n     * 获取加载参数\n     */\n    )\n  }, {\n    key: \"getLoadParams\",\n    value: function getLoadParams(container) {\n      var params = {\n        batch_size: this.config.batchSize\n      };\n\n      // 从数据属性中获取参数\n      Object.keys(container.dataset).forEach(function (key) {\n        if (key.startsWith('param')) {\n          var paramName = key.replace('param', '').toLowerCase();\n          params[paramName] = container.dataset[key];\n        }\n      });\n      return params;\n    }\n\n    /**\n     * 渲染记录\n     */\n  }, {\n    key: \"renderRecords\",\n    value: (function () {\n      var _renderRecords = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(container, records) {\n        var _this2 = this;\n        var contentContainer, html, tempDiv;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              contentContainer = container.querySelector('.notion-progressive-content');\n              if (contentContainer) {\n                _context3.n = 1;\n                break;\n              }\n              throw new Error('未找到内容容器');\n            case 1:\n              html = records.map(function (record) {\n                return _this2.renderRecord(record);\n              }).join(''); // 使用淡入动画添加内容\n              tempDiv = document.createElement('div');\n              tempDiv.innerHTML = html;\n              tempDiv.style.opacity = '0';\n              tempDiv.style.transition = 'opacity 0.3s ease-in-out';\n              contentContainer.appendChild(tempDiv);\n\n              // 触发淡入动画\n              setTimeout(function () {\n                tempDiv.style.opacity = '1';\n              }, 10);\n\n              // 动画完成后移除包装div\n              setTimeout(function () {\n                while (tempDiv.firstChild) {\n                  contentContainer.appendChild(tempDiv.firstChild);\n                }\n                tempDiv.remove();\n              }, 300);\n            case 2:\n              return _context3.a(2);\n          }\n        }, _callee3);\n      }));\n      function renderRecords(_x3, _x4) {\n        return _renderRecords.apply(this, arguments);\n      }\n      return renderRecords;\n    }()\n    /**\n     * 渲染单个记录\n     */\n    )\n  }, {\n    key: \"renderRecord\",\n    value: function renderRecord(record) {\n      var title = this.extractTitle(record.properties);\n      var id = record.id.substring(0, 8);\n      return \"\\n      <div class=\\\"notion-database-record\\\" data-record-id=\\\"\".concat(record.id, \"\\\">\\n        <div class=\\\"notion-record-title\\\">\").concat(this.escapeHtml(title), \"</div>\\n        <div class=\\\"notion-record-properties\\\">\\n          <div class=\\\"notion-record-property\\\">\\n            <span class=\\\"notion-property-name\\\">ID:</span>\\n            <span class=\\\"notion-property-value\\\">\").concat(id, \"...</span>\\n          </div>\\n          <div class=\\\"notion-record-property\\\">\\n            <span class=\\\"notion-property-name\\\">\\u521B\\u5EFA\\u65F6\\u95F4:</span>\\n            <span class=\\\"notion-property-value\\\">\").concat(this.formatDate(record.created_time), \"</span>\\n          </div>\\n        </div>\\n      </div>\\n    \");\n    }\n\n    /**\n     * 提取标题\n     */\n  }, {\n    key: \"extractTitle\",\n    value: function extractTitle(properties) {\n      for (var _i = 0, _Object$values = Object.values(properties); _i < _Object$values.length; _i++) {\n        var property = _Object$values[_i];\n        if (property.type === 'title' && property.title && property.title.length > 0) {\n          return property.title[0].plain_text || '无标题';\n        }\n      }\n      return '无标题';\n    }\n\n    /**\n     * 设置加载状态\n     */\n  }, {\n    key: \"setLoadingState\",\n    value: function setLoadingState(button, loading) {\n      var loadingText = button.querySelector('.notion-loading-text');\n      var loadingSpinner = button.querySelector('.notion-loading-spinner');\n      var buttonText = button.querySelector('.notion-button-text');\n      if (loading) {\n        button.disabled = true;\n        if (buttonText) buttonText.style.display = 'none';\n        if (loadingText) loadingText.style.display = 'inline';\n        if (loadingSpinner) loadingSpinner.style.display = 'inline';\n      } else {\n        button.disabled = false;\n        if (buttonText) buttonText.style.display = 'inline';\n        if (loadingText) loadingText.style.display = 'none';\n        if (loadingSpinner) loadingSpinner.style.display = 'none';\n      }\n    }\n\n    /**\n     * 处理加载错误\n     */\n  }, {\n    key: \"handleLoadError\",\n    value: function handleLoadError(button, error) {\n      var _this3 = this;\n      var loadingText = button.querySelector('.notion-loading-text');\n      if (loadingText) {\n        loadingText.textContent = '加载失败，请重试';\n        loadingText.style.display = 'inline';\n      }\n      console.error('📄 [渐进式加载] 加载失败:', error);\n\n      // 3秒后恢复按钮状态\n      setTimeout(function () {\n        if (loadingText) {\n          loadingText.textContent = '加载中...';\n        }\n        _this3.setLoadingState(button, false);\n      }, 3000);\n    }\n\n    /**\n     * 隐藏加载更多按钮\n     */\n  }, {\n    key: \"hideLoadMoreButton\",\n    value: function hideLoadMoreButton(button) {\n      var buttonContainer = button.parentElement;\n      if (buttonContainer) {\n        buttonContainer.style.transition = 'opacity 0.3s ease-out';\n        buttonContainer.style.opacity = '0';\n        setTimeout(function () {\n          buttonContainer.style.display = 'none';\n        }, 300);\n      }\n    }\n\n    /**\n     * 生成容器ID\n     */\n  }, {\n    key: \"generateContainerId\",\n    value: function generateContainerId() {\n      return \"progressive-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substring(2, 9));\n    }\n\n    /**\n     * 转义HTML\n     */\n  }, {\n    key: \"escapeHtml\",\n    value: function escapeHtml(text) {\n      var div = document.createElement('div');\n      div.textContent = text;\n      return div.innerHTML;\n    }\n\n    /**\n     * 格式化日期\n     */\n  }, {\n    key: \"formatDate\",\n    value: function formatDate(dateString) {\n      try {\n        var date = new Date(dateString);\n        return date.toLocaleDateString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit'\n        });\n      } catch (_unused) {\n        return dateString;\n      }\n    }\n\n    /**\n     * 获取配置\n     */\n  }, {\n    key: \"getConfig\",\n    value: function getConfig() {\n      return _objectSpread({}, this.config);\n    }\n\n    /**\n     * 更新配置\n     */\n  }, {\n    key: \"updateConfig\",\n    value: function updateConfig(newConfig) {\n      this.config = _objectSpread(_objectSpread({}, this.config), newConfig);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('progressive:config:updated', this.config);\n    }\n\n    /**\n     * 获取加载状态\n     */\n  }, {\n    key: \"getLoadingStates\",\n    value: function getLoadingStates() {\n      return new Map(this.loadingStates);\n    }\n\n    /**\n     * 清除加载状态\n     */\n  }, {\n    key: \"clearLoadingState\",\n    value: function clearLoadingState(containerId) {\n      this.loadingStates.delete(containerId);\n      this.loadedData.delete(containerId);\n    }\n\n    /**\n     * 销毁渐进式加载器\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      document.removeEventListener('click', this.handleLoadMoreClick);\n      this.loadingStates.clear();\n      this.loadedData.clear();\n      ProgressiveLoader.instance = null;\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('progressive:loader:destroyed');\n      console.log('📄 [渐进式加载] 已销毁');\n    }\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance(config) {\n      if (!ProgressiveLoader.instance) {\n        ProgressiveLoader.instance = new ProgressiveLoader(config);\n      }\n      return ProgressiveLoader.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_ProgressiveLoader = ProgressiveLoader;\n_defineProperty(ProgressiveLoader, \"instance\", null);\nvar progressiveLoader = ProgressiveLoader.getInstance();\n\n// 自动初始化\n\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_30__.ready)(function () {\n  progressiveLoader;\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressiveLoader);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/ProgressiveLoader.ts\n\n}");

/***/ }),

/***/ "./src/frontend/components/ResourceOptimizer.ts":
/*!******************************************************!*\
  !*** ./src/frontend/components/ResourceOptimizer.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResourceOptimizer: () => (/* binding */ ResourceOptimizer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   resourceOptimizer: () => (/* binding */ resourceOptimizer)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ \"./node_modules/core-js/modules/es.array.filter.js\");\n/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.from.js */ \"./node_modules/core-js/modules/es.array.from.js\");\n/* harmony import */ var core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_from_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.includes.js */ \"./node_modules/core-js/modules/es.array.includes.js\");\n/* harmony import */ var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */ \"./node_modules/core-js/modules/es.array.slice.js\");\n/* harmony import */ var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ \"./node_modules/core-js/modules/es.function.name.js\");\n/* harmony import */ var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.map.js */ \"./node_modules/core-js/modules/es.map.js\");\n/* harmony import */ var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ \"./node_modules/core-js/modules/es.object.keys.js\");\n/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptor.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptor.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.object.get-own-property-descriptors.js */ \"./node_modules/core-js/modules/es.object.get-own-property-descriptors.js\");\n/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ \"./node_modules/core-js/modules/es.regexp.exec.js\");\n/* harmony import */ var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ \"./node_modules/core-js/modules/es.regexp.to-string.js\");\n/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! core-js/modules/es.set.js */ \"./node_modules/core-js/modules/es.set.js\");\n/* harmony import */ var core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_set_js__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ \"./node_modules/core-js/modules/es.string.includes.js\");\n/* harmony import */ var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ../../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\nvar _ResourceOptimizer;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 资源优化器 - 现代化TypeScript版本\n * \n * 从原有resource-optimizer.js完全迁移，包括：\n * - CDN集成和回退\n * - 预测性加载\n * - 智能缓存策略\n * - 性能监控\n */\n\n\n\n/**\n * 资源优化器类\n */\nvar ResourceOptimizer = /*#__PURE__*/function () {\n  function ResourceOptimizer() {\n    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _classCallCheck(this, ResourceOptimizer);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"metrics\", void 0);\n    _defineProperty(this, \"userBehavior\", void 0);\n    _defineProperty(this, \"resourceCache\", new Map());\n    _defineProperty(this, \"predictiveQueue\", new Set());\n    _defineProperty(this, \"intersectionObserver\", null);\n    _defineProperty(this, \"performanceTimer\", null);\n    if (ResourceOptimizer.instance) {\n      return ResourceOptimizer.instance;\n    }\n    ResourceOptimizer.instance = this;\n    this.config = _objectSpread({\n      cdn: {\n        enabled: false,\n        baseUrl: '',\n        fallbackEnabled: true,\n        timeout: 5000\n      },\n      lazyLoading: {\n        enhanced: true,\n        preloadThreshold: 2,\n        retryAttempts: 3,\n        retryDelay: 1000\n      },\n      performance: {\n        enabled: true,\n        reportInterval: 30000,\n        metricsEndpoint: ''\n      },\n      predictiveLoading: {\n        enabled: true,\n        hoverDelay: 100,\n        scrollThreshold: 0.8,\n        maxPredictions: 5,\n        confidenceThreshold: 0.7\n      },\n      smartCache: {\n        enabled: true,\n        maxCacheSize: 50 * 1024 * 1024,\n        // 50MB\n        ttl: 24 * 60 * 60 * 1000,\n        // 24小时\n        compressionEnabled: true,\n        versionCheck: true\n      }\n    }, config);\n    this.metrics = {\n      loadTimes: [],\n      errors: [],\n      cacheHits: 0,\n      totalRequests: 0,\n      predictiveHits: 0,\n      predictiveAttempts: 0,\n      cacheSize: 0\n    };\n    this.userBehavior = {\n      scrollSpeed: 0,\n      hoverTargets: [],\n      clickPatterns: [],\n      lastActivity: Date.now()\n    };\n    this.init();\n  }\n\n  /**\n   * 获取单例实例\n   */\n  return _createClass(ResourceOptimizer, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化资源优化器\n     */\n    function init() {\n      this.setupUserBehaviorTracking();\n      this.setupPredictiveLoading();\n      this.setupPerformanceMonitoring();\n      this.setupSmartCache();\n      console.log('⚡ [资源优化器] 已初始化');\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:optimizer:initialized');\n    }\n\n    /**\n     * 设置用户行为追踪\n     */\n  }, {\n    key: \"setupUserBehaviorTracking\",\n    value: function setupUserBehaviorTracking() {\n      var _this = this;\n      var lastScrollY = window.scrollY;\n      var scrollStartTime = Date.now();\n\n      // 滚动行为追踪\n      var handleScroll = this.throttle(function () {\n        var currentScrollY = window.scrollY;\n        var currentTime = Date.now();\n        var distance = Math.abs(currentScrollY - lastScrollY);\n        var time = currentTime - scrollStartTime;\n        if (time > 0) {\n          _this.userBehavior.scrollSpeed = distance / time;\n        }\n        lastScrollY = currentScrollY;\n        scrollStartTime = currentTime;\n        _this.userBehavior.lastActivity = currentTime;\n      }, 100);\n\n      // 鼠标悬停追踪\n      var handleMouseOver = function handleMouseOver(e) {\n        var target = e.target;\n        if (target.tagName === 'A') {\n          var href = target.href;\n          if (href && !_this.userBehavior.hoverTargets.includes(href)) {\n            _this.userBehavior.hoverTargets.push(href);\n            _this.predictResource(href);\n          }\n        }\n        _this.userBehavior.lastActivity = Date.now();\n      };\n\n      // 点击模式追踪\n      var handleClick = function handleClick(e) {\n        var target = e.target;\n        if (target.tagName === 'A') {\n          var href = target.href;\n          if (href) {\n            _this.userBehavior.clickPatterns.push(href);\n            // 保持最近20个点击记录\n            if (_this.userBehavior.clickPatterns.length > 20) {\n              _this.userBehavior.clickPatterns.shift();\n            }\n          }\n        }\n        _this.userBehavior.lastActivity = Date.now();\n      };\n      window.addEventListener('scroll', handleScroll, {\n        passive: true\n      });\n      document.addEventListener('mouseover', handleMouseOver, {\n        passive: true\n      });\n      document.addEventListener('click', handleClick, {\n        passive: true\n      });\n    }\n\n    /**\n     * 设置预测性加载\n     */\n  }, {\n    key: \"setupPredictiveLoading\",\n    value: function setupPredictiveLoading() {\n      var _this2 = this;\n      if (!this.config.predictiveLoading.enabled) return;\n\n      // 创建Intersection Observer用于预测性加载\n      this.intersectionObserver = new IntersectionObserver(function (entries) {\n        entries.forEach(function (entry) {\n          if (entry.isIntersecting) {\n            var element = entry.target;\n            _this2.analyzePredictiveOpportunity(element);\n          }\n        });\n      }, {\n        rootMargin: '100px',\n        threshold: 0.1\n      });\n\n      // 观察所有链接\n      document.querySelectorAll('a[href]').forEach(function (link) {\n        _this2.intersectionObserver.observe(link);\n      });\n    }\n\n    /**\n     * 分析预测性加载机会\n     */\n  }, {\n    key: \"analyzePredictiveOpportunity\",\n    value: function analyzePredictiveOpportunity(element) {\n      if (element.tagName !== 'A') return;\n      var href = element.href;\n      if (!href || this.predictiveQueue.has(href)) return;\n\n      // 计算预测置信度\n      var confidence = this.calculatePredictionConfidence(href);\n      if (confidence >= this.config.predictiveLoading.confidenceThreshold) {\n        this.predictResource(href);\n      }\n    }\n\n    /**\n     * 计算预测置信度\n     */\n  }, {\n    key: \"calculatePredictionConfidence\",\n    value: function calculatePredictionConfidence(href) {\n      var confidence = 0;\n\n      // 基于悬停历史\n      if (this.userBehavior.hoverTargets.includes(href)) {\n        confidence += 0.3;\n      }\n\n      // 基于点击模式\n      var clickCount = this.userBehavior.clickPatterns.filter(function (pattern) {\n        return pattern === href;\n      }).length;\n      confidence += Math.min(clickCount * 0.2, 0.4);\n\n      // 基于滚动速度（慢速滚动表示用户在仔细阅读）\n      if (this.userBehavior.scrollSpeed < 1) {\n        confidence += 0.2;\n      }\n\n      // 基于活跃度\n      var timeSinceLastActivity = Date.now() - this.userBehavior.lastActivity;\n      if (timeSinceLastActivity < 5000) {\n        // 5秒内有活动\n        confidence += 0.1;\n      }\n      return Math.min(confidence, 1);\n    }\n\n    /**\n     * 预测性资源加载\n     */\n  }, {\n    key: \"predictResource\",\n    value: function predictResource(href) {\n      var _this3 = this;\n      if (this.predictiveQueue.size >= this.config.predictiveLoading.maxPredictions) {\n        return;\n      }\n      this.predictiveQueue.add(href);\n      this.metrics.predictiveAttempts++;\n\n      // 延迟预加载以避免影响当前页面性能\n      setTimeout(function () {\n        _this3.preloadResource(href);\n      }, this.config.predictiveLoading.hoverDelay);\n    }\n\n    /**\n     * 预加载资源\n     */\n  }, {\n    key: \"preloadResource\",\n    value: (function () {\n      var _preloadResource = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(href) {\n        var link;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              try {\n                link = document.createElement('link');\n                link.rel = 'prefetch';\n                link.href = href;\n                document.head.appendChild(link);\n                this.metrics.predictiveHits++;\n                (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:predicted', {\n                  href: href,\n                  success: true\n                });\n              } catch (error) {\n                console.warn('⚡ [预测性加载] 失败:', href, error);\n                (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:predicted', {\n                  href: href,\n                  success: false,\n                  error: error\n                });\n              }\n            case 1:\n              return _context.a(2);\n          }\n        }, _callee, this);\n      }));\n      function preloadResource(_x) {\n        return _preloadResource.apply(this, arguments);\n      }\n      return preloadResource;\n    }()\n    /**\n     * 设置性能监控\n     */\n    )\n  }, {\n    key: \"setupPerformanceMonitoring\",\n    value: function setupPerformanceMonitoring() {\n      var _this4 = this;\n      if (!this.config.performance.enabled) return;\n\n      // 监控资源加载时间\n      if ('PerformanceObserver' in window) {\n        var observer = new PerformanceObserver(function (list) {\n          list.getEntries().forEach(function (entry) {\n            if (entry.entryType === 'resource') {\n              _this4.metrics.loadTimes.push(entry.duration);\n              _this4.metrics.totalRequests++;\n            }\n          });\n        });\n        observer.observe({\n          entryTypes: ['resource']\n        });\n      }\n\n      // 定期报告性能指标\n      if (this.config.performance.reportInterval > 0) {\n        this.performanceTimer = setInterval(function () {\n          _this4.reportPerformanceMetrics();\n        }, this.config.performance.reportInterval);\n      }\n    }\n\n    /**\n     * 报告性能指标\n     */\n  }, {\n    key: \"reportPerformanceMetrics\",\n    value: function reportPerformanceMetrics() {\n      var metrics = this.getPerformanceMetrics();\n      console.log('⚡ [性能指标]', metrics);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:performance:report', metrics);\n\n      // 如果配置了端点，发送到服务器\n      if (this.config.performance.metricsEndpoint) {\n        this.sendMetricsToServer(metrics);\n      }\n    }\n\n    /**\n     * 发送指标到服务器\n     */\n  }, {\n    key: \"sendMetricsToServer\",\n    value: (function () {\n      var _sendMetricsToServer = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(metrics) {\n        var _t;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.p = _context2.n) {\n            case 0:\n              _context2.p = 0;\n              _context2.n = 1;\n              return fetch(this.config.performance.metricsEndpoint, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(metrics)\n              });\n            case 1:\n              _context2.n = 3;\n              break;\n            case 2:\n              _context2.p = 2;\n              _t = _context2.v;\n              console.warn('⚡ [性能指标] 发送失败:', _t);\n            case 3:\n              return _context2.a(2);\n          }\n        }, _callee2, this, [[0, 2]]);\n      }));\n      function sendMetricsToServer(_x2) {\n        return _sendMetricsToServer.apply(this, arguments);\n      }\n      return sendMetricsToServer;\n    }()\n    /**\n     * 设置智能缓存\n     */\n    )\n  }, {\n    key: \"setupSmartCache\",\n    value: function setupSmartCache() {\n      var _this5 = this;\n      if (!this.config.smartCache.enabled) return;\n\n      // 从localStorage恢复缓存\n      this.loadCacheFromStorage();\n\n      // 定期清理过期缓存\n      setInterval(function () {\n        _this5.cleanExpiredCache();\n      }, 60000); // 每分钟检查一次\n    }\n\n    /**\n     * 从存储加载缓存\n     */\n  }, {\n    key: \"loadCacheFromStorage\",\n    value: function loadCacheFromStorage() {\n      try {\n        var cached = localStorage.getItem('notion-resource-cache');\n        if (cached) {\n          var data = JSON.parse(cached);\n          this.resourceCache = new Map(data.entries);\n          this.metrics.cacheSize = data.size || 0;\n        }\n      } catch (error) {\n        console.warn('⚡ [智能缓存] 加载失败:', error);\n      }\n    }\n\n    /**\n     * 清理过期缓存\n     */\n  }, {\n    key: \"cleanExpiredCache\",\n    value: function cleanExpiredCache() {\n      var now = Date.now();\n      var cleaned = 0;\n      var _iterator = _createForOfIteratorHelper(this.resourceCache.entries()),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var _step$value = _slicedToArray(_step.value, 2),\n            key = _step$value[0],\n            value = _step$value[1];\n          if (value.expires && value.expires < now) {\n            this.resourceCache.delete(key);\n            cleaned++;\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      if (cleaned > 0) {\n        this.saveCacheToStorage();\n        console.log(\"\\u26A1 [\\u667A\\u80FD\\u7F13\\u5B58] \\u6E05\\u7406\\u8FC7\\u671F\\u9879: \".concat(cleaned));\n      }\n    }\n\n    /**\n     * 保存缓存到存储\n     */\n  }, {\n    key: \"saveCacheToStorage\",\n    value: function saveCacheToStorage() {\n      try {\n        var data = {\n          entries: Array.from(this.resourceCache.entries()),\n          size: this.metrics.cacheSize,\n          timestamp: Date.now()\n        };\n        localStorage.setItem('notion-resource-cache', JSON.stringify(data));\n      } catch (error) {\n        console.warn('⚡ [智能缓存] 保存失败:', error);\n      }\n    }\n\n    /**\n     * 节流函数\n     */\n  }, {\n    key: \"throttle\",\n    value: function throttle(func, limit) {\n      var _this6 = this;\n      var inThrottle = false;\n      return function () {\n        if (!inThrottle) {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          func.apply(_this6, args);\n          inThrottle = true;\n          setTimeout(function () {\n            inThrottle = false;\n          }, limit);\n        }\n      };\n    }\n\n    /**\n     * 获取性能指标\n     */\n  }, {\n    key: \"getPerformanceMetrics\",\n    value: function getPerformanceMetrics() {\n      var averageLoadTime = this.metrics.loadTimes.length > 0 ? this.metrics.loadTimes.reduce(function (a, b) {\n        return a + b;\n      }, 0) / this.metrics.loadTimes.length : 0;\n      var cacheHitRate = this.metrics.totalRequests > 0 ? this.metrics.cacheHits / this.metrics.totalRequests : 0;\n      var predictiveHitRate = this.metrics.predictiveAttempts > 0 ? this.metrics.predictiveHits / this.metrics.predictiveAttempts : 0;\n      return _objectSpread(_objectSpread({}, this.metrics), {}, {\n        averageLoadTime: averageLoadTime,\n        cacheHitRate: cacheHitRate,\n        predictiveHitRate: predictiveHitRate\n      });\n    }\n\n    /**\n     * 获取配置\n     */\n  }, {\n    key: \"getConfig\",\n    value: function getConfig() {\n      return _objectSpread({}, this.config);\n    }\n\n    /**\n     * 更新配置\n     */\n  }, {\n    key: \"updateConfig\",\n    value: function updateConfig(newConfig) {\n      this.config = _objectSpread(_objectSpread({}, this.config), newConfig);\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:config:updated', this.config);\n    }\n\n    /**\n     * 销毁资源优化器\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (this.intersectionObserver) {\n        this.intersectionObserver.disconnect();\n        this.intersectionObserver = null;\n      }\n      if (this.performanceTimer) {\n        clearInterval(this.performanceTimer);\n        this.performanceTimer = null;\n      }\n      this.saveCacheToStorage();\n      this.resourceCache.clear();\n      this.predictiveQueue.clear();\n      ResourceOptimizer.instance = null;\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_27__.emit)('resource:optimizer:destroyed');\n      console.log('⚡ [资源优化器] 已销毁');\n    }\n  }], [{\n    key: \"getInstance\",\n    value: function getInstance(config) {\n      if (!ResourceOptimizer.instance) {\n        ResourceOptimizer.instance = new ResourceOptimizer(config);\n      }\n      return ResourceOptimizer.instance;\n    }\n  }]);\n}();\n\n// 导出单例实例\n_ResourceOptimizer = ResourceOptimizer;\n_defineProperty(ResourceOptimizer, \"instance\", null);\nvar resourceOptimizer = ResourceOptimizer.getInstance();\n\n// 自动初始化\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_28__.ready)(function () {\n  resourceOptimizer;\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResourceOptimizer);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/frontend/components/ResourceOptimizer.ts\n\n}");

/***/ }),

/***/ "./src/frontend/frontend.ts":
/*!**********************************!*\
  !*** ./src/frontend/frontend.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   frontendApp: () => (/* binding */ frontendApp)\n/* harmony export */ });\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ \"./node_modules/core-js/modules/es.symbol.js\");\n/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ \"./node_modules/core-js/modules/es.symbol.description.js\");\n/* harmony import */ var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ \"./node_modules/core-js/modules/es.symbol.iterator.js\");\n/* harmony import */ var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.symbol.to-primitive.js */ \"./node_modules/core-js/modules/es.symbol.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_to_primitive_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ \"./node_modules/core-js/modules/es.array.iterator.js\");\n/* harmony import */ var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.date.to-primitive.js */ \"./node_modules/core-js/modules/es.date.to-primitive.js\");\n/* harmony import */ var core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_primitive_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ \"./node_modules/core-js/modules/es.number.constructor.js\");\n/* harmony import */ var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.get-prototype-of.js */ \"./node_modules/core-js/modules/es.object.get-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_prototype_of_js__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.object.set-prototype-of.js */ \"./node_modules/core-js/modules/es.object.set-prototype-of.js\");\n/* harmony import */ var core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_set_prototype_of_js__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ \"./node_modules/core-js/modules/es.object.to-string.js\");\n/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.promise.js */ \"./node_modules/core-js/modules/es.promise.js\");\n/* harmony import */ var core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_js__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ \"./node_modules/core-js/modules/es.string.iterator.js\");\n/* harmony import */ var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/web.dom-collections.for-each.js */ \"./node_modules/core-js/modules/web.dom-collections.for-each.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ \"./node_modules/core-js/modules/web.dom-collections.iterator.js\");\n/* harmony import */ var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _shared_utils_dom__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../shared/utils/dom */ \"./src/shared/utils/dom.ts\");\n/* harmony import */ var _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../shared/core/EventBus */ \"./src/shared/core/EventBus.ts\");\n/* harmony import */ var _FrontendContent__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./FrontendContent */ \"./src/frontend/FrontendContent.ts\");\n/* harmony import */ var _styles_frontend_frontend_scss__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../styles/frontend/frontend.scss */ \"./src/styles/frontend/frontend.scss\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * 前端入口文件\n */\n\n\n\n\n\n// 导入样式\n\n\n/**\n * 前端应用主类\n */\nvar FrontendApp = /*#__PURE__*/function () {\n  function FrontendApp() {\n    _classCallCheck(this, FrontendApp);\n    _defineProperty(this, \"initialized\", false);\n  }\n  return _createClass(FrontendApp, [{\n    key: \"init\",\n    value:\n    /**\n     * 初始化应用\n     */\n    function init() {\n      if (this.initialized) {\n        return;\n      }\n      console.log('🚀 Notion to WordPress Frontend App initializing...');\n\n      // 初始化组件\n      this.initializeComponents();\n\n      // 绑定事件\n      this.bindEvents();\n      this.initialized = true;\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:initialized');\n      console.log('✅ Notion to WordPress Frontend App initialized');\n    }\n\n    /**\n     * 初始化组件\n     */\n  }, {\n    key: \"initializeComponents\",\n    value: function initializeComponents() {\n      // 初始化现代化前端内容渲染系统\n      this.initializeFrontendContent();\n\n      // 初始化Notion块渲染器\n      this.initNotionBlocks();\n\n      // 初始化懒加载\n      this.initLazyLoading();\n\n      // 初始化数学公式渲染\n      this.initMathRendering();\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:components:init');\n    }\n\n    /**\n     * 初始化Notion块\n     */\n  }, {\n    key: \"initNotionBlocks\",\n    value: function initNotionBlocks() {\n      var notionBlocks = document.querySelectorAll('.notion-block');\n      console.log(\"Found \".concat(notionBlocks.length, \" Notion blocks\"));\n      notionBlocks.forEach(function (block) {\n        // 这里将处理各种Notion块类型\n        var blockType = block.getAttribute('data-block-type');\n        (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:block:init', {\n          block: block,\n          blockType: blockType\n        });\n      });\n    }\n\n    /**\n     * 初始化懒加载\n     */\n  }, {\n    key: \"initLazyLoading\",\n    value: function initLazyLoading() {\n      if ('IntersectionObserver' in window) {\n        var lazyImages = document.querySelectorAll('img[data-src]');\n        if (lazyImages.length > 0) {\n          var imageObserver = new IntersectionObserver(function (entries) {\n            entries.forEach(function (entry) {\n              if (entry.isIntersecting) {\n                var img = entry.target;\n                var src = img.getAttribute('data-src');\n                if (src) {\n                  img.src = src;\n                  img.removeAttribute('data-src');\n                  imageObserver.unobserve(img);\n                  (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:image:loaded', {\n                    img: img,\n                    src: src\n                  });\n                }\n              }\n            });\n          });\n          lazyImages.forEach(function (img) {\n            return imageObserver.observe(img);\n          });\n          console.log(\"Initialized lazy loading for \".concat(lazyImages.length, \" images\"));\n        }\n      }\n    }\n\n    /**\n     * 初始化数学公式渲染\n     */\n  }, {\n    key: \"initMathRendering\",\n    value: function initMathRendering() {\n      var mathElements = document.querySelectorAll('.notion-equation');\n      if (mathElements.length > 0) {\n        console.log(\"Found \".concat(mathElements.length, \" math equations\"));\n\n        // 动态加载KaTeX（如果需要）\n        this.loadMathRenderer().then(function () {\n          mathElements.forEach(function (element) {\n            (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:math:render', {\n              element: element\n            });\n          });\n        });\n      }\n    }\n\n    /**\n     * 动态加载数学渲染器\n     */\n  }, {\n    key: \"loadMathRenderer\",\n    value: (function () {\n      var _loadMathRenderer = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              return _context.a(2, new Promise(function (resolve) {\n                // 模拟异步加载\n                setTimeout(resolve, 100);\n              }));\n          }\n        }, _callee);\n      }));\n      function loadMathRenderer() {\n        return _loadMathRenderer.apply(this, arguments);\n      }\n      return loadMathRenderer;\n    }()\n    /**\n     * 初始化前端内容渲染系统\n     */\n    )\n  }, {\n    key: \"initializeFrontendContent\",\n    value: function initializeFrontendContent() {\n      // 初始化现代化的前端内容渲染系统\n      _FrontendContent__WEBPACK_IMPORTED_MODULE_16__.frontendContent.init();\n\n      // 监听前端内容系统事件\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.eventBus.on('frontend:content:initialized', function () {\n        console.log('✅ 前端内容渲染系统已初始化');\n      });\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.eventBus.on('frontend:content:destroyed', function () {\n        console.log('🔥 前端内容渲染系统已销毁');\n      });\n\n      // 监听性能报告事件\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.eventBus.on('frontend:performance:report', function (_event, metrics) {\n        console.log('📊 前端性能报告:', metrics);\n      });\n      console.log('✅ 前端内容渲染系统已初始化');\n    }\n\n    /**\n     * 绑定事件\n     */\n  }, {\n    key: \"bindEvents\",\n    value: function bindEvents() {\n      // 监听页面滚动\n      var scrollTimeout;\n      window.addEventListener('scroll', function () {\n        clearTimeout(scrollTimeout);\n        scrollTimeout = setTimeout(function () {\n          (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:scroll', {\n            scrollY: window.scrollY,\n            scrollX: window.scrollX\n          });\n        }, 100);\n      });\n\n      // 监听窗口大小变化\n      window.addEventListener('resize', function () {\n        (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:resize', {\n          width: window.innerWidth,\n          height: window.innerHeight\n        });\n      });\n    }\n\n    /**\n     * 销毁应用\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (!this.initialized) {\n        return;\n      }\n\n      // 清理前端内容渲染系统\n      _FrontendContent__WEBPACK_IMPORTED_MODULE_16__.frontendContent.destroy();\n      (0,_shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.emit)('frontend:destroy');\n      _shared_core_EventBus__WEBPACK_IMPORTED_MODULE_15__.eventBus.removeAllListeners();\n      this.initialized = false;\n      console.log('🔥 Notion to WordPress Frontend App destroyed');\n    }\n  }]);\n}();\n/**\n * 创建全局应用实例\n */\nvar frontendApp = new FrontendApp();\n\n/**\n * 导出到全局作用域\n */\n\nwindow.NotionWpFrontend = frontendApp;\n\n/**\n * DOM准备就绪后初始化\n */\n(0,_shared_utils_dom__WEBPACK_IMPORTED_MODULE_14__.ready)(function () {\n  frontendApp.init();\n});\n\n/**\n * 导出主要功能\n */\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (frontendApp);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZnJvbnRlbmQvZnJvbnRlbmQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBQ0EsdUtBQUFBLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLHdCQUFBQyxNQUFBLEdBQUFBLE1BQUEsT0FBQUMsQ0FBQSxHQUFBRixDQUFBLENBQUFHLFFBQUEsa0JBQUFDLENBQUEsR0FBQUosQ0FBQSxDQUFBSyxXQUFBLDhCQUFBQyxFQUFBTixDQUFBLEVBQUFFLENBQUEsRUFBQUUsQ0FBQSxFQUFBRSxDQUFBLFFBQUFDLENBQUEsR0FBQUwsQ0FBQSxJQUFBQSxDQUFBLENBQUFNLFNBQUEsWUFBQUMsU0FBQSxHQUFBUCxDQUFBLEdBQUFPLFNBQUEsRUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLE1BQUEsQ0FBQUwsQ0FBQSxDQUFBQyxTQUFBLFVBQUFLLG1CQUFBLENBQUFILENBQUEsdUJBQUFWLENBQUEsRUFBQUUsQ0FBQSxFQUFBRSxDQUFBLFFBQUFFLENBQUEsRUFBQUMsQ0FBQSxFQUFBRyxDQUFBLEVBQUFJLENBQUEsTUFBQUMsQ0FBQSxHQUFBWCxDQUFBLFFBQUFZLENBQUEsT0FBQUMsQ0FBQSxLQUFBRixDQUFBLEtBQUFiLENBQUEsS0FBQWdCLENBQUEsRUFBQXBCLENBQUEsRUFBQXFCLENBQUEsRUFBQUMsQ0FBQSxFQUFBTixDQUFBLEVBQUFNLENBQUEsQ0FBQUMsSUFBQSxDQUFBdkIsQ0FBQSxNQUFBc0IsQ0FBQSxXQUFBQSxFQUFBckIsQ0FBQSxFQUFBQyxDQUFBLFdBQUFNLENBQUEsR0FBQVAsQ0FBQSxFQUFBUSxDQUFBLE1BQUFHLENBQUEsR0FBQVosQ0FBQSxFQUFBbUIsQ0FBQSxDQUFBZixDQUFBLEdBQUFGLENBQUEsRUFBQW1CLENBQUEsZ0JBQUFDLEVBQUFwQixDQUFBLEVBQUFFLENBQUEsU0FBQUssQ0FBQSxHQUFBUCxDQUFBLEVBQUFVLENBQUEsR0FBQVIsQ0FBQSxFQUFBSCxDQUFBLE9BQUFpQixDQUFBLElBQUFGLENBQUEsS0FBQVYsQ0FBQSxJQUFBTCxDQUFBLEdBQUFnQixDQUFBLENBQUFPLE1BQUEsRUFBQXZCLENBQUEsVUFBQUssQ0FBQSxFQUFBRSxDQUFBLEdBQUFTLENBQUEsQ0FBQWhCLENBQUEsR0FBQXFCLENBQUEsR0FBQUgsQ0FBQSxDQUFBRixDQUFBLEVBQUFRLENBQUEsR0FBQWpCLENBQUEsS0FBQU4sQ0FBQSxRQUFBSSxDQUFBLEdBQUFtQixDQUFBLEtBQUFyQixDQUFBLE1BQUFRLENBQUEsR0FBQUosQ0FBQSxFQUFBQyxDQUFBLEdBQUFELENBQUEsWUFBQUMsQ0FBQSxXQUFBRCxDQUFBLE1BQUFBLENBQUEsTUFBQVIsQ0FBQSxJQUFBUSxDQUFBLE9BQUFjLENBQUEsTUFBQWhCLENBQUEsR0FBQUosQ0FBQSxRQUFBb0IsQ0FBQSxHQUFBZCxDQUFBLFFBQUFDLENBQUEsTUFBQVUsQ0FBQSxDQUFBQyxDQUFBLEdBQUFoQixDQUFBLEVBQUFlLENBQUEsQ0FBQWYsQ0FBQSxHQUFBSSxDQUFBLE9BQUFjLENBQUEsR0FBQUcsQ0FBQSxLQUFBbkIsQ0FBQSxHQUFBSixDQUFBLFFBQUFNLENBQUEsTUFBQUosQ0FBQSxJQUFBQSxDQUFBLEdBQUFxQixDQUFBLE1BQUFqQixDQUFBLE1BQUFOLENBQUEsRUFBQU0sQ0FBQSxNQUFBSixDQUFBLEVBQUFlLENBQUEsQ0FBQWYsQ0FBQSxHQUFBcUIsQ0FBQSxFQUFBaEIsQ0FBQSxjQUFBSCxDQUFBLElBQUFKLENBQUEsYUFBQW1CLENBQUEsUUFBQUgsQ0FBQSxPQUFBZCxDQUFBLHFCQUFBRSxDQUFBLEVBQUFXLENBQUEsRUFBQVEsQ0FBQSxRQUFBVCxDQUFBLFlBQUFVLFNBQUEsdUNBQUFSLENBQUEsVUFBQUQsQ0FBQSxJQUFBSyxDQUFBLENBQUFMLENBQUEsRUFBQVEsQ0FBQSxHQUFBaEIsQ0FBQSxHQUFBUSxDQUFBLEVBQUFMLENBQUEsR0FBQWEsQ0FBQSxHQUFBeEIsQ0FBQSxHQUFBUSxDQUFBLE9BQUFULENBQUEsR0FBQVksQ0FBQSxNQUFBTSxDQUFBLEtBQUFWLENBQUEsS0FBQUMsQ0FBQSxHQUFBQSxDQUFBLFFBQUFBLENBQUEsU0FBQVUsQ0FBQSxDQUFBZixDQUFBLFFBQUFrQixDQUFBLENBQUFiLENBQUEsRUFBQUcsQ0FBQSxLQUFBTyxDQUFBLENBQUFmLENBQUEsR0FBQVEsQ0FBQSxHQUFBTyxDQUFBLENBQUFDLENBQUEsR0FBQVIsQ0FBQSxhQUFBSSxDQUFBLE1BQUFSLENBQUEsUUFBQUMsQ0FBQSxLQUFBSCxDQUFBLFlBQUFMLENBQUEsR0FBQU8sQ0FBQSxDQUFBRixDQUFBLFdBQUFMLENBQUEsR0FBQUEsQ0FBQSxDQUFBMEIsSUFBQSxDQUFBbkIsQ0FBQSxFQUFBSSxDQUFBLFVBQUFjLFNBQUEsMkNBQUF6QixDQUFBLENBQUEyQixJQUFBLFNBQUEzQixDQUFBLEVBQUFXLENBQUEsR0FBQVgsQ0FBQSxDQUFBNEIsS0FBQSxFQUFBcEIsQ0FBQSxTQUFBQSxDQUFBLG9CQUFBQSxDQUFBLEtBQUFSLENBQUEsR0FBQU8sQ0FBQSxDQUFBc0IsTUFBQSxLQUFBN0IsQ0FBQSxDQUFBMEIsSUFBQSxDQUFBbkIsQ0FBQSxHQUFBQyxDQUFBLFNBQUFHLENBQUEsR0FBQWMsU0FBQSx1Q0FBQXBCLENBQUEsZ0JBQUFHLENBQUEsT0FBQUQsQ0FBQSxHQUFBUixDQUFBLGNBQUFDLENBQUEsSUFBQWlCLENBQUEsR0FBQUMsQ0FBQSxDQUFBZixDQUFBLFFBQUFRLENBQUEsR0FBQVYsQ0FBQSxDQUFBeUIsSUFBQSxDQUFBdkIsQ0FBQSxFQUFBZSxDQUFBLE9BQUFFLENBQUEsa0JBQUFwQixDQUFBLElBQUFPLENBQUEsR0FBQVIsQ0FBQSxFQUFBUyxDQUFBLE1BQUFHLENBQUEsR0FBQVgsQ0FBQSxjQUFBZSxDQUFBLG1CQUFBYSxLQUFBLEVBQUE1QixDQUFBLEVBQUEyQixJQUFBLEVBQUFWLENBQUEsU0FBQWhCLENBQUEsRUFBQUksQ0FBQSxFQUFBRSxDQUFBLFFBQUFJLENBQUEsUUFBQVMsQ0FBQSxnQkFBQVYsVUFBQSxjQUFBb0Isa0JBQUEsY0FBQUMsMkJBQUEsS0FBQS9CLENBQUEsR0FBQVksTUFBQSxDQUFBb0IsY0FBQSxNQUFBeEIsQ0FBQSxNQUFBTCxDQUFBLElBQUFILENBQUEsQ0FBQUEsQ0FBQSxJQUFBRyxDQUFBLFNBQUFXLG1CQUFBLENBQUFkLENBQUEsT0FBQUcsQ0FBQSxpQ0FBQUgsQ0FBQSxHQUFBVyxDQUFBLEdBQUFvQiwwQkFBQSxDQUFBdEIsU0FBQSxHQUFBQyxTQUFBLENBQUFELFNBQUEsR0FBQUcsTUFBQSxDQUFBQyxNQUFBLENBQUFMLENBQUEsWUFBQU8sRUFBQWhCLENBQUEsV0FBQWEsTUFBQSxDQUFBcUIsY0FBQSxHQUFBckIsTUFBQSxDQUFBcUIsY0FBQSxDQUFBbEMsQ0FBQSxFQUFBZ0MsMEJBQUEsS0FBQWhDLENBQUEsQ0FBQW1DLFNBQUEsR0FBQUgsMEJBQUEsRUFBQWpCLG1CQUFBLENBQUFmLENBQUEsRUFBQU0sQ0FBQSx5QkFBQU4sQ0FBQSxDQUFBVSxTQUFBLEdBQUFHLE1BQUEsQ0FBQUMsTUFBQSxDQUFBRixDQUFBLEdBQUFaLENBQUEsV0FBQStCLGlCQUFBLENBQUFyQixTQUFBLEdBQUFzQiwwQkFBQSxFQUFBakIsbUJBQUEsQ0FBQUgsQ0FBQSxpQkFBQW9CLDBCQUFBLEdBQUFqQixtQkFBQSxDQUFBaUIsMEJBQUEsaUJBQUFELGlCQUFBLEdBQUFBLGlCQUFBLENBQUFLLFdBQUEsd0JBQUFyQixtQkFBQSxDQUFBaUIsMEJBQUEsRUFBQTFCLENBQUEsd0JBQUFTLG1CQUFBLENBQUFILENBQUEsR0FBQUcsbUJBQUEsQ0FBQUgsQ0FBQSxFQUFBTixDQUFBLGdCQUFBUyxtQkFBQSxDQUFBSCxDQUFBLEVBQUFSLENBQUEsaUNBQUFXLG1CQUFBLENBQUFILENBQUEsOERBQUF5QixZQUFBLFlBQUFBLGFBQUEsYUFBQUMsQ0FBQSxFQUFBOUIsQ0FBQSxFQUFBK0IsQ0FBQSxFQUFBdkIsQ0FBQTtBQUFBLFNBQUFELG9CQUFBZixDQUFBLEVBQUFFLENBQUEsRUFBQUUsQ0FBQSxFQUFBSCxDQUFBLFFBQUFPLENBQUEsR0FBQUssTUFBQSxDQUFBMkIsY0FBQSxRQUFBaEMsQ0FBQSx1QkFBQVIsQ0FBQSxJQUFBUSxDQUFBLFFBQUFPLG1CQUFBLFlBQUEwQixtQkFBQXpDLENBQUEsRUFBQUUsQ0FBQSxFQUFBRSxDQUFBLEVBQUFILENBQUEsYUFBQUssRUFBQUosQ0FBQSxFQUFBRSxDQUFBLElBQUFXLG1CQUFBLENBQUFmLENBQUEsRUFBQUUsQ0FBQSxZQUFBRixDQUFBLGdCQUFBMEMsT0FBQSxDQUFBeEMsQ0FBQSxFQUFBRSxDQUFBLEVBQUFKLENBQUEsU0FBQUUsQ0FBQSxHQUFBTSxDQUFBLEdBQUFBLENBQUEsQ0FBQVIsQ0FBQSxFQUFBRSxDQUFBLElBQUEyQixLQUFBLEVBQUF6QixDQUFBLEVBQUF1QyxVQUFBLEdBQUExQyxDQUFBLEVBQUEyQyxZQUFBLEdBQUEzQyxDQUFBLEVBQUE0QyxRQUFBLEdBQUE1QyxDQUFBLE1BQUFELENBQUEsQ0FBQUUsQ0FBQSxJQUFBRSxDQUFBLElBQUFFLENBQUEsYUFBQUEsQ0FBQSxjQUFBQSxDQUFBLG1CQUFBUyxtQkFBQSxDQUFBZixDQUFBLEVBQUFFLENBQUEsRUFBQUUsQ0FBQSxFQUFBSCxDQUFBO0FBQUEsU0FBQTZDLG1CQUFBMUMsQ0FBQSxFQUFBSCxDQUFBLEVBQUFELENBQUEsRUFBQUUsQ0FBQSxFQUFBSSxDQUFBLEVBQUFlLENBQUEsRUFBQVosQ0FBQSxjQUFBRCxDQUFBLEdBQUFKLENBQUEsQ0FBQWlCLENBQUEsRUFBQVosQ0FBQSxHQUFBRyxDQUFBLEdBQUFKLENBQUEsQ0FBQXFCLEtBQUEsV0FBQXpCLENBQUEsZ0JBQUFKLENBQUEsQ0FBQUksQ0FBQSxLQUFBSSxDQUFBLENBQUFvQixJQUFBLEdBQUEzQixDQUFBLENBQUFXLENBQUEsSUFBQW1DLE9BQUEsQ0FBQUMsT0FBQSxDQUFBcEMsQ0FBQSxFQUFBcUMsSUFBQSxDQUFBL0MsQ0FBQSxFQUFBSSxDQUFBO0FBQUEsU0FBQTRDLGtCQUFBOUMsQ0FBQSw2QkFBQUgsQ0FBQSxTQUFBRCxDQUFBLEdBQUFtRCxTQUFBLGFBQUFKLE9BQUEsV0FBQTdDLENBQUEsRUFBQUksQ0FBQSxRQUFBZSxDQUFBLEdBQUFqQixDQUFBLENBQUFnRCxLQUFBLENBQUFuRCxDQUFBLEVBQUFELENBQUEsWUFBQXFELE1BQUFqRCxDQUFBLElBQUEwQyxrQkFBQSxDQUFBekIsQ0FBQSxFQUFBbkIsQ0FBQSxFQUFBSSxDQUFBLEVBQUErQyxLQUFBLEVBQUFDLE1BQUEsVUFBQWxELENBQUEsY0FBQWtELE9BQUFsRCxDQUFBLElBQUEwQyxrQkFBQSxDQUFBekIsQ0FBQSxFQUFBbkIsQ0FBQSxFQUFBSSxDQUFBLEVBQUErQyxLQUFBLEVBQUFDLE1BQUEsV0FBQWxELENBQUEsS0FBQWlELEtBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsU0FBQUUsZ0JBQUFsQyxDQUFBLEVBQUFqQixDQUFBLFVBQUFpQixDQUFBLFlBQUFqQixDQUFBLGFBQUFzQixTQUFBO0FBQUEsU0FBQThCLGtCQUFBeEQsQ0FBQSxFQUFBRSxDQUFBLGFBQUFELENBQUEsTUFBQUEsQ0FBQSxHQUFBQyxDQUFBLENBQUFzQixNQUFBLEVBQUF2QixDQUFBLFVBQUFLLENBQUEsR0FBQUosQ0FBQSxDQUFBRCxDQUFBLEdBQUFLLENBQUEsQ0FBQXFDLFVBQUEsR0FBQXJDLENBQUEsQ0FBQXFDLFVBQUEsUUFBQXJDLENBQUEsQ0FBQXNDLFlBQUEsa0JBQUF0QyxDQUFBLEtBQUFBLENBQUEsQ0FBQXVDLFFBQUEsUUFBQWhDLE1BQUEsQ0FBQTJCLGNBQUEsQ0FBQXhDLENBQUEsRUFBQXlELGNBQUEsQ0FBQW5ELENBQUEsQ0FBQW9ELEdBQUEsR0FBQXBELENBQUE7QUFBQSxTQUFBcUQsYUFBQTNELENBQUEsRUFBQUUsQ0FBQSxFQUFBRCxDQUFBLFdBQUFDLENBQUEsSUFBQXNELGlCQUFBLENBQUF4RCxDQUFBLENBQUFVLFNBQUEsRUFBQVIsQ0FBQSxHQUFBRCxDQUFBLElBQUF1RCxpQkFBQSxDQUFBeEQsQ0FBQSxFQUFBQyxDQUFBLEdBQUFZLE1BQUEsQ0FBQTJCLGNBQUEsQ0FBQXhDLENBQUEsaUJBQUE2QyxRQUFBLFNBQUE3QyxDQUFBO0FBQUEsU0FBQTRELGdCQUFBNUQsQ0FBQSxFQUFBRSxDQUFBLEVBQUFELENBQUEsWUFBQUMsQ0FBQSxHQUFBdUQsY0FBQSxDQUFBdkQsQ0FBQSxNQUFBRixDQUFBLEdBQUFhLE1BQUEsQ0FBQTJCLGNBQUEsQ0FBQXhDLENBQUEsRUFBQUUsQ0FBQSxJQUFBMkIsS0FBQSxFQUFBNUIsQ0FBQSxFQUFBMEMsVUFBQSxNQUFBQyxZQUFBLE1BQUFDLFFBQUEsVUFBQTdDLENBQUEsQ0FBQUUsQ0FBQSxJQUFBRCxDQUFBLEVBQUFELENBQUE7QUFBQSxTQUFBeUQsZUFBQXhELENBQUEsUUFBQU8sQ0FBQSxHQUFBcUQsWUFBQSxDQUFBNUQsQ0FBQSxnQ0FBQTZELE9BQUEsQ0FBQXRELENBQUEsSUFBQUEsQ0FBQSxHQUFBQSxDQUFBO0FBQUEsU0FBQXFELGFBQUE1RCxDQUFBLEVBQUFDLENBQUEsb0JBQUE0RCxPQUFBLENBQUE3RCxDQUFBLE1BQUFBLENBQUEsU0FBQUEsQ0FBQSxNQUFBRCxDQUFBLEdBQUFDLENBQUEsQ0FBQUUsTUFBQSxDQUFBNEQsV0FBQSxrQkFBQS9ELENBQUEsUUFBQVEsQ0FBQSxHQUFBUixDQUFBLENBQUEyQixJQUFBLENBQUExQixDQUFBLEVBQUFDLENBQUEsZ0NBQUE0RCxPQUFBLENBQUF0RCxDQUFBLFVBQUFBLENBQUEsWUFBQWtCLFNBQUEseUVBQUF4QixDQUFBLEdBQUE4RCxNQUFBLEdBQUFDLE1BQUEsRUFBQWhFLENBQUE7QUFEQTtBQUNBO0FBQ0E7O0FBRTRDO0FBQ2E7QUFDTDs7QUFFcEQ7QUFDMEM7O0FBRTFDO0FBQ0E7QUFDQTtBQUZBLElBR01xRSxXQUFXO0VBQUEsU0FBQUEsWUFBQTtJQUFBZixlQUFBLE9BQUFlLFdBQUE7SUFBQVYsZUFBQSxzQkFDTyxLQUFLO0VBQUE7RUFBQSxPQUFBRCxZQUFBLENBQUFXLFdBQUE7SUFBQVosR0FBQTtJQUFBN0IsS0FBQTtJQUUzQjtBQUNGO0FBQ0E7SUFDRSxTQUFBMEMsSUFBSUEsQ0FBQSxFQUFTO01BQ1gsSUFBSSxJQUFJLENBQUNDLFdBQVcsRUFBRTtRQUNwQjtNQUNGO01BRUFDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHFEQUFxRCxDQUFDOztNQUVsRTtNQUNBLElBQUksQ0FBQ0Msb0JBQW9CLENBQUMsQ0FBQzs7TUFFM0I7TUFDQSxJQUFJLENBQUNDLFVBQVUsQ0FBQyxDQUFDO01BRWpCLElBQUksQ0FBQ0osV0FBVyxHQUFHLElBQUk7TUFDdkJKLDREQUFJLENBQUMsc0JBQXNCLENBQUM7TUFFNUJLLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdEQUFnRCxDQUFDO0lBQy9EOztJQUVBO0FBQ0Y7QUFDQTtFQUZFO0lBQUFoQixHQUFBO0lBQUE3QixLQUFBLEVBR0EsU0FBUThDLG9CQUFvQkEsQ0FBQSxFQUFTO01BQ25DO01BQ0EsSUFBSSxDQUFDRSx5QkFBeUIsQ0FBQyxDQUFDOztNQUVoQztNQUNBLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUMsQ0FBQzs7TUFFdkI7TUFDQSxJQUFJLENBQUNDLGVBQWUsQ0FBQyxDQUFDOztNQUV0QjtNQUNBLElBQUksQ0FBQ0MsaUJBQWlCLENBQUMsQ0FBQztNQUV4QlosNERBQUksQ0FBQywwQkFBMEIsQ0FBQztJQUNsQzs7SUFFQTtBQUNGO0FBQ0E7RUFGRTtJQUFBVixHQUFBO0lBQUE3QixLQUFBLEVBR0EsU0FBUWlELGdCQUFnQkEsQ0FBQSxFQUFTO01BQy9CLElBQU1HLFlBQVksR0FBR0MsUUFBUSxDQUFDQyxnQkFBZ0IsQ0FBQyxlQUFlLENBQUM7TUFDL0RWLE9BQU8sQ0FBQ0MsR0FBRyxVQUFBVSxNQUFBLENBQVVILFlBQVksQ0FBQ3pELE1BQU0sbUJBQWdCLENBQUM7TUFFekR5RCxZQUFZLENBQUNJLE9BQU8sQ0FBQyxVQUFBQyxLQUFLLEVBQUk7UUFDNUI7UUFDQSxJQUFNQyxTQUFTLEdBQUdELEtBQUssQ0FBQ0UsWUFBWSxDQUFDLGlCQUFpQixDQUFDO1FBQ3ZEcEIsNERBQUksQ0FBQyxxQkFBcUIsRUFBRTtVQUFFa0IsS0FBSyxFQUFMQSxLQUFLO1VBQUVDLFNBQVMsRUFBVEE7UUFBVSxDQUFDLENBQUM7TUFDbkQsQ0FBQyxDQUFDO0lBQ0o7O0lBRUE7QUFDRjtBQUNBO0VBRkU7SUFBQTdCLEdBQUE7SUFBQTdCLEtBQUEsRUFHQSxTQUFRa0QsZUFBZUEsQ0FBQSxFQUFTO01BQzlCLElBQUksc0JBQXNCLElBQUlVLE1BQU0sRUFBRTtRQUNwQyxJQUFNQyxVQUFVLEdBQUdSLFFBQVEsQ0FBQ0MsZ0JBQWdCLENBQUMsZUFBZSxDQUFDO1FBRTdELElBQUlPLFVBQVUsQ0FBQ2xFLE1BQU0sR0FBRyxDQUFDLEVBQUU7VUFDekIsSUFBTW1FLGFBQWEsR0FBRyxJQUFJQyxvQkFBb0IsQ0FBQyxVQUFDQyxPQUFPLEVBQUs7WUFDMURBLE9BQU8sQ0FBQ1IsT0FBTyxDQUFDLFVBQUFTLEtBQUssRUFBSTtjQUN2QixJQUFJQSxLQUFLLENBQUNDLGNBQWMsRUFBRTtnQkFDeEIsSUFBTUMsR0FBRyxHQUFHRixLQUFLLENBQUNHLE1BQTBCO2dCQUM1QyxJQUFNQyxHQUFHLEdBQUdGLEdBQUcsQ0FBQ1IsWUFBWSxDQUFDLFVBQVUsQ0FBQztnQkFFeEMsSUFBSVUsR0FBRyxFQUFFO2tCQUNQRixHQUFHLENBQUNFLEdBQUcsR0FBR0EsR0FBRztrQkFDYkYsR0FBRyxDQUFDRyxlQUFlLENBQUMsVUFBVSxDQUFDO2tCQUMvQlIsYUFBYSxDQUFDUyxTQUFTLENBQUNKLEdBQUcsQ0FBQztrQkFFNUI1Qiw0REFBSSxDQUFDLHVCQUF1QixFQUFFO29CQUFFNEIsR0FBRyxFQUFIQSxHQUFHO29CQUFFRSxHQUFHLEVBQUhBO2tCQUFJLENBQUMsQ0FBQztnQkFDN0M7Y0FDRjtZQUNGLENBQUMsQ0FBQztVQUNKLENBQUMsQ0FBQztVQUVGUixVQUFVLENBQUNMLE9BQU8sQ0FBQyxVQUFBVyxHQUFHO1lBQUEsT0FBSUwsYUFBYSxDQUFDVSxPQUFPLENBQUNMLEdBQUcsQ0FBQztVQUFBLEVBQUM7VUFDckR2QixPQUFPLENBQUNDLEdBQUcsaUNBQUFVLE1BQUEsQ0FBaUNNLFVBQVUsQ0FBQ2xFLE1BQU0sWUFBUyxDQUFDO1FBQ3pFO01BQ0Y7SUFDRjs7SUFFQTtBQUNGO0FBQ0E7RUFGRTtJQUFBa0MsR0FBQTtJQUFBN0IsS0FBQSxFQUdBLFNBQVFtRCxpQkFBaUJBLENBQUEsRUFBUztNQUNoQyxJQUFNc0IsWUFBWSxHQUFHcEIsUUFBUSxDQUFDQyxnQkFBZ0IsQ0FBQyxrQkFBa0IsQ0FBQztNQUVsRSxJQUFJbUIsWUFBWSxDQUFDOUUsTUFBTSxHQUFHLENBQUMsRUFBRTtRQUMzQmlELE9BQU8sQ0FBQ0MsR0FBRyxVQUFBVSxNQUFBLENBQVVrQixZQUFZLENBQUM5RSxNQUFNLG9CQUFpQixDQUFDOztRQUUxRDtRQUNBLElBQUksQ0FBQytFLGdCQUFnQixDQUFDLENBQUMsQ0FBQ3RELElBQUksQ0FBQyxZQUFNO1VBQ2pDcUQsWUFBWSxDQUFDakIsT0FBTyxDQUFDLFVBQUFtQixPQUFPLEVBQUk7WUFDOUJwQyw0REFBSSxDQUFDLHNCQUFzQixFQUFFO2NBQUVvQyxPQUFPLEVBQVBBO1lBQVEsQ0FBQyxDQUFDO1VBQzNDLENBQUMsQ0FBQztRQUNKLENBQUMsQ0FBQztNQUNKO0lBQ0Y7O0lBRUE7QUFDRjtBQUNBO0VBRkU7SUFBQTlDLEdBQUE7SUFBQTdCLEtBQUE7TUFBQSxJQUFBNEUsaUJBQUEsR0FBQXZELGlCQUFBLGNBQUFiLFlBQUEsR0FBQUUsQ0FBQSxDQUdBLFNBQUFtRSxRQUFBO1FBQUEsT0FBQXJFLFlBQUEsR0FBQUMsQ0FBQSxXQUFBcUUsUUFBQTtVQUFBLGtCQUFBQSxRQUFBLENBQUF2RyxDQUFBO1lBQUE7Y0FBQSxPQUFBdUcsUUFBQSxDQUFBdEYsQ0FBQSxJQUVTLElBQUkwQixPQUFPLENBQUMsVUFBQ0MsT0FBTyxFQUFLO2dCQUM5QjtnQkFDQTRELFVBQVUsQ0FBQzVELE9BQU8sRUFBRSxHQUFHLENBQUM7Y0FDMUIsQ0FBQyxDQUFDO1VBQUE7UUFBQSxHQUFBMEQsT0FBQTtNQUFBLENBQ0g7TUFBQSxTQU5hSCxnQkFBZ0JBLENBQUE7UUFBQSxPQUFBRSxpQkFBQSxDQUFBckQsS0FBQSxPQUFBRCxTQUFBO01BQUE7TUFBQSxPQUFoQm9ELGdCQUFnQjtJQUFBO0lBUTlCO0FBQ0Y7QUFDQTtJQUZFO0VBQUE7SUFBQTdDLEdBQUE7SUFBQTdCLEtBQUEsRUFHQSxTQUFRZ0QseUJBQXlCQSxDQUFBLEVBQVM7TUFDeEM7TUFDQVIsOERBQWUsQ0FBQ0UsSUFBSSxDQUFDLENBQUM7O01BRXRCO01BQ0FKLDREQUFRLENBQUMwQyxFQUFFLENBQUMsOEJBQThCLEVBQUUsWUFBTTtRQUNoRHBDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQixDQUFDO01BQy9CLENBQUMsQ0FBQztNQUVGUCw0REFBUSxDQUFDMEMsRUFBRSxDQUFDLDRCQUE0QixFQUFFLFlBQU07UUFDOUNwQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQkFBZ0IsQ0FBQztNQUMvQixDQUFDLENBQUM7O01BRUY7TUFDQVAsNERBQVEsQ0FBQzBDLEVBQUUsQ0FBQyw2QkFBNkIsRUFBRSxVQUFDQyxNQUFNLEVBQUVDLE9BQU8sRUFBSztRQUM5RHRDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFlBQVksRUFBRXFDLE9BQU8sQ0FBQztNQUNwQyxDQUFDLENBQUM7TUFFRnRDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQixDQUFDO0lBQy9COztJQUVBO0FBQ0Y7QUFDQTtFQUZFO0lBQUFoQixHQUFBO0lBQUE3QixLQUFBLEVBR0EsU0FBUStDLFVBQVVBLENBQUEsRUFBUztNQUN6QjtNQUNBLElBQUlvQyxhQUE2QjtNQUNqQ3ZCLE1BQU0sQ0FBQ3dCLGdCQUFnQixDQUFDLFFBQVEsRUFBRSxZQUFNO1FBQ3RDQyxZQUFZLENBQUNGLGFBQWEsQ0FBQztRQUMzQkEsYUFBYSxHQUFHSixVQUFVLENBQUMsWUFBTTtVQUMvQnhDLDREQUFJLENBQUMsaUJBQWlCLEVBQUU7WUFDdEIrQyxPQUFPLEVBQUUxQixNQUFNLENBQUMwQixPQUFPO1lBQ3ZCQyxPQUFPLEVBQUUzQixNQUFNLENBQUMyQjtVQUNsQixDQUFDLENBQUM7UUFDSixDQUFDLEVBQUUsR0FBRyxDQUFDO01BQ1QsQ0FBQyxDQUFDOztNQUVGO01BQ0EzQixNQUFNLENBQUN3QixnQkFBZ0IsQ0FBQyxRQUFRLEVBQUUsWUFBTTtRQUN0QzdDLDREQUFJLENBQUMsaUJBQWlCLEVBQUU7VUFDdEJpRCxLQUFLLEVBQUU1QixNQUFNLENBQUM2QixVQUFVO1VBQ3hCQyxNQUFNLEVBQUU5QixNQUFNLENBQUMrQjtRQUNqQixDQUFDLENBQUM7TUFDSixDQUFDLENBQUM7SUFDSjs7SUFFQTtBQUNGO0FBQ0E7RUFGRTtJQUFBOUQsR0FBQTtJQUFBN0IsS0FBQSxFQUdBLFNBQUE0RixPQUFPQSxDQUFBLEVBQVM7TUFDZCxJQUFJLENBQUMsSUFBSSxDQUFDakQsV0FBVyxFQUFFO1FBQ3JCO01BQ0Y7O01BRUE7TUFDQUgsOERBQWUsQ0FBQ29ELE9BQU8sQ0FBQyxDQUFDO01BRXpCckQsNERBQUksQ0FBQyxrQkFBa0IsQ0FBQztNQUN4QkQsNERBQVEsQ0FBQ3VELGtCQUFrQixDQUFDLENBQUM7TUFDN0IsSUFBSSxDQUFDbEQsV0FBVyxHQUFHLEtBQUs7TUFFeEJDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtDQUErQyxDQUFDO0lBQzlEO0VBQUM7QUFBQTtBQUdIO0FBQ0E7QUFDQTtBQUNBLElBQU1pRCxXQUFXLEdBQUcsSUFBSXJELFdBQVcsQ0FBQyxDQUFDOztBQUVyQztBQUNBO0FBQ0E7O0FBT0FtQixNQUFNLENBQUNtQyxnQkFBZ0IsR0FBR0QsV0FBVzs7QUFFckM7QUFDQTtBQUNBO0FBQ0F6RCx5REFBSyxDQUFDLFlBQU07RUFDVnlELFdBQVcsQ0FBQ3BELElBQUksQ0FBQyxDQUFDO0FBQ3BCLENBQUMsQ0FBQzs7QUFFRjtBQUNBO0FBQ0E7QUFDdUI7QUFDdkIsaUVBQWVvRCxXQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm90aW9uLXRvLXdvcmRwcmVzcy8uL3NyYy9mcm9udGVuZC9mcm9udGVuZC50cz83MjFmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5YmN56uv5YWl5Y+j5paH5Lu2XG4gKi9cblxuaW1wb3J0IHsgcmVhZHkgfSBmcm9tICcuLi9zaGFyZWQvdXRpbHMvZG9tJztcbmltcG9ydCB7IGV2ZW50QnVzLCBlbWl0IH0gZnJvbSAnLi4vc2hhcmVkL2NvcmUvRXZlbnRCdXMnO1xuaW1wb3J0IHsgZnJvbnRlbmRDb250ZW50IH0gZnJvbSAnLi9Gcm9udGVuZENvbnRlbnQnO1xuXG4vLyDlr7zlhaXmoLflvI9cbmltcG9ydCAnLi4vc3R5bGVzL2Zyb250ZW5kL2Zyb250ZW5kLnNjc3MnO1xuXG4vKipcbiAqIOWJjeerr+W6lOeUqOS4u+exu1xuICovXG5jbGFzcyBGcm9udGVuZEFwcCB7XG4gIHByaXZhdGUgaW5pdGlhbGl6ZWQgPSBmYWxzZTtcblxuICAvKipcbiAgICog5Yid5aeL5YyW5bqU55SoXG4gICAqL1xuICBpbml0KCk6IHZvaWQge1xuICAgIGlmICh0aGlzLmluaXRpYWxpemVkKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ/CfmoAgTm90aW9uIHRvIFdvcmRQcmVzcyBGcm9udGVuZCBBcHAgaW5pdGlhbGl6aW5nLi4uJyk7XG5cbiAgICAvLyDliJ3lp4vljJbnu4Tku7ZcbiAgICB0aGlzLmluaXRpYWxpemVDb21wb25lbnRzKCk7XG5cbiAgICAvLyDnu5Hlrprkuovku7ZcbiAgICB0aGlzLmJpbmRFdmVudHMoKTtcblxuICAgIHRoaXMuaW5pdGlhbGl6ZWQgPSB0cnVlO1xuICAgIGVtaXQoJ2Zyb250ZW5kOmluaXRpYWxpemVkJyk7XG5cbiAgICBjb25zb2xlLmxvZygn4pyFIE5vdGlvbiB0byBXb3JkUHJlc3MgRnJvbnRlbmQgQXBwIGluaXRpYWxpemVkJyk7XG4gIH1cblxuICAvKipcbiAgICog5Yid5aeL5YyW57uE5Lu2XG4gICAqL1xuICBwcml2YXRlIGluaXRpYWxpemVDb21wb25lbnRzKCk6IHZvaWQge1xuICAgIC8vIOWIneWni+WMlueOsOS7o+WMluWJjeerr+WGheWuuea4suafk+ezu+e7n1xuICAgIHRoaXMuaW5pdGlhbGl6ZUZyb250ZW5kQ29udGVudCgpO1xuXG4gICAgLy8g5Yid5aeL5YyWTm90aW9u5Z2X5riy5p+T5ZmoXG4gICAgdGhpcy5pbml0Tm90aW9uQmxvY2tzKCk7XG5cbiAgICAvLyDliJ3lp4vljJbmh5LliqDovb1cbiAgICB0aGlzLmluaXRMYXp5TG9hZGluZygpO1xuXG4gICAgLy8g5Yid5aeL5YyW5pWw5a2m5YWs5byP5riy5p+TXG4gICAgdGhpcy5pbml0TWF0aFJlbmRlcmluZygpO1xuXG4gICAgZW1pdCgnZnJvbnRlbmQ6Y29tcG9uZW50czppbml0Jyk7XG4gIH1cblxuICAvKipcbiAgICog5Yid5aeL5YyWTm90aW9u5Z2XXG4gICAqL1xuICBwcml2YXRlIGluaXROb3Rpb25CbG9ja3MoKTogdm9pZCB7XG4gICAgY29uc3Qgbm90aW9uQmxvY2tzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLm5vdGlvbi1ibG9jaycpO1xuICAgIGNvbnNvbGUubG9nKGBGb3VuZCAke25vdGlvbkJsb2Nrcy5sZW5ndGh9IE5vdGlvbiBibG9ja3NgKTtcblxuICAgIG5vdGlvbkJsb2Nrcy5mb3JFYWNoKGJsb2NrID0+IHtcbiAgICAgIC8vIOi/memHjOWwhuWkhOeQhuWQhOenjU5vdGlvbuWdl+exu+Wei1xuICAgICAgY29uc3QgYmxvY2tUeXBlID0gYmxvY2suZ2V0QXR0cmlidXRlKCdkYXRhLWJsb2NrLXR5cGUnKTtcbiAgICAgIGVtaXQoJ2Zyb250ZW5kOmJsb2NrOmluaXQnLCB7IGJsb2NrLCBibG9ja1R5cGUgfSk7XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICog5Yid5aeL5YyW5oeS5Yqg6L29XG4gICAqL1xuICBwcml2YXRlIGluaXRMYXp5TG9hZGluZygpOiB2b2lkIHtcbiAgICBpZiAoJ0ludGVyc2VjdGlvbk9ic2VydmVyJyBpbiB3aW5kb3cpIHtcbiAgICAgIGNvbnN0IGxhenlJbWFnZXMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdpbWdbZGF0YS1zcmNdJyk7XG4gICAgICBcbiAgICAgIGlmIChsYXp5SW1hZ2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgaW1hZ2VPYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlcigoZW50cmllcykgPT4ge1xuICAgICAgICAgIGVudHJpZXMuZm9yRWFjaChlbnRyeSA9PiB7XG4gICAgICAgICAgICBpZiAoZW50cnkuaXNJbnRlcnNlY3RpbmcpIHtcbiAgICAgICAgICAgICAgY29uc3QgaW1nID0gZW50cnkudGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XG4gICAgICAgICAgICAgIGNvbnN0IHNyYyA9IGltZy5nZXRBdHRyaWJ1dGUoJ2RhdGEtc3JjJyk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICBpZiAoc3JjKSB7XG4gICAgICAgICAgICAgICAgaW1nLnNyYyA9IHNyYztcbiAgICAgICAgICAgICAgICBpbWcucmVtb3ZlQXR0cmlidXRlKCdkYXRhLXNyYycpO1xuICAgICAgICAgICAgICAgIGltYWdlT2JzZXJ2ZXIudW5vYnNlcnZlKGltZyk7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgZW1pdCgnZnJvbnRlbmQ6aW1hZ2U6bG9hZGVkJywgeyBpbWcsIHNyYyB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcblxuICAgICAgICBsYXp5SW1hZ2VzLmZvckVhY2goaW1nID0+IGltYWdlT2JzZXJ2ZXIub2JzZXJ2ZShpbWcpKTtcbiAgICAgICAgY29uc29sZS5sb2coYEluaXRpYWxpemVkIGxhenkgbG9hZGluZyBmb3IgJHtsYXp5SW1hZ2VzLmxlbmd0aH0gaW1hZ2VzYCk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWIneWni+WMluaVsOWtpuWFrOW8j+a4suafk1xuICAgKi9cbiAgcHJpdmF0ZSBpbml0TWF0aFJlbmRlcmluZygpOiB2b2lkIHtcbiAgICBjb25zdCBtYXRoRWxlbWVudHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubm90aW9uLWVxdWF0aW9uJyk7XG4gICAgXG4gICAgaWYgKG1hdGhFbGVtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zb2xlLmxvZyhgRm91bmQgJHttYXRoRWxlbWVudHMubGVuZ3RofSBtYXRoIGVxdWF0aW9uc2ApO1xuICAgICAgXG4gICAgICAvLyDliqjmgIHliqDovb1LYVRlWO+8iOWmguaenOmcgOimge+8iVxuICAgICAgdGhpcy5sb2FkTWF0aFJlbmRlcmVyKCkudGhlbigoKSA9PiB7XG4gICAgICAgIG1hdGhFbGVtZW50cy5mb3JFYWNoKGVsZW1lbnQgPT4ge1xuICAgICAgICAgIGVtaXQoJ2Zyb250ZW5kOm1hdGg6cmVuZGVyJywgeyBlbGVtZW50IH0pO1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDliqjmgIHliqDovb3mlbDlrabmuLLmn5PlmahcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgbG9hZE1hdGhSZW5kZXJlcigpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICAvLyDov5nph4zlsIbliqjmgIHliqDovb1LYVRlWOaIluWFtuS7luaVsOWtpua4suafk+W6k1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4ge1xuICAgICAgLy8g5qih5ouf5byC5q2l5Yqg6L29XG4gICAgICBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCk7XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICog5Yid5aeL5YyW5YmN56uv5YaF5a655riy5p+T57O757ufXG4gICAqL1xuICBwcml2YXRlIGluaXRpYWxpemVGcm9udGVuZENvbnRlbnQoKTogdm9pZCB7XG4gICAgLy8g5Yid5aeL5YyW546w5Luj5YyW55qE5YmN56uv5YaF5a655riy5p+T57O757ufXG4gICAgZnJvbnRlbmRDb250ZW50LmluaXQoKTtcblxuICAgIC8vIOebkeWQrOWJjeerr+WGheWuueezu+e7n+S6i+S7tlxuICAgIGV2ZW50QnVzLm9uKCdmcm9udGVuZDpjb250ZW50OmluaXRpYWxpemVkJywgKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ+KchSDliY3nq6/lhoXlrrnmuLLmn5Pns7vnu5/lt7LliJ3lp4vljJYnKTtcbiAgICB9KTtcblxuICAgIGV2ZW50QnVzLm9uKCdmcm9udGVuZDpjb250ZW50OmRlc3Ryb3llZCcsICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SlIOWJjeerr+WGheWuuea4suafk+ezu+e7n+W3sumUgOavgScpO1xuICAgIH0pO1xuXG4gICAgLy8g55uR5ZCs5oCn6IO95oql5ZGK5LqL5Lu2XG4gICAgZXZlbnRCdXMub24oJ2Zyb250ZW5kOnBlcmZvcm1hbmNlOnJlcG9ydCcsIChfZXZlbnQsIG1ldHJpY3MpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOWJjeerr+aAp+iDveaKpeWRijonLCBtZXRyaWNzKTtcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCfinIUg5YmN56uv5YaF5a655riy5p+T57O757uf5bey5Yid5aeL5YyWJyk7XG4gIH1cblxuICAvKipcbiAgICog57uR5a6a5LqL5Lu2XG4gICAqL1xuICBwcml2YXRlIGJpbmRFdmVudHMoKTogdm9pZCB7XG4gICAgLy8g55uR5ZCs6aG16Z2i5rua5YqoXG4gICAgbGV0IHNjcm9sbFRpbWVvdXQ6IE5vZGVKUy5UaW1lb3V0O1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCAoKSA9PiB7XG4gICAgICBjbGVhclRpbWVvdXQoc2Nyb2xsVGltZW91dCk7XG4gICAgICBzY3JvbGxUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGVtaXQoJ2Zyb250ZW5kOnNjcm9sbCcsIHtcbiAgICAgICAgICBzY3JvbGxZOiB3aW5kb3cuc2Nyb2xsWSxcbiAgICAgICAgICBzY3JvbGxYOiB3aW5kb3cuc2Nyb2xsWFxuICAgICAgICB9KTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfSk7XG5cbiAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJZcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgKCkgPT4ge1xuICAgICAgZW1pdCgnZnJvbnRlbmQ6cmVzaXplJywge1xuICAgICAgICB3aWR0aDogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgIGhlaWdodDogd2luZG93LmlubmVySGVpZ2h0XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiDplIDmr4HlupTnlKhcbiAgICovXG4gIGRlc3Ryb3koKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLmluaXRpYWxpemVkKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g5riF55CG5YmN56uv5YaF5a655riy5p+T57O757ufXG4gICAgZnJvbnRlbmRDb250ZW50LmRlc3Ryb3koKTtcblxuICAgIGVtaXQoJ2Zyb250ZW5kOmRlc3Ryb3knKTtcbiAgICBldmVudEJ1cy5yZW1vdmVBbGxMaXN0ZW5lcnMoKTtcbiAgICB0aGlzLmluaXRpYWxpemVkID0gZmFsc2U7XG5cbiAgICBjb25zb2xlLmxvZygn8J+UpSBOb3Rpb24gdG8gV29yZFByZXNzIEZyb250ZW5kIEFwcCBkZXN0cm95ZWQnKTtcbiAgfVxufVxuXG4vKipcbiAqIOWIm+W7uuWFqOWxgOW6lOeUqOWunuS+i1xuICovXG5jb25zdCBmcm9udGVuZEFwcCA9IG5ldyBGcm9udGVuZEFwcCgpO1xuXG4vKipcbiAqIOWvvOWHuuWIsOWFqOWxgOS9nOeUqOWfn1xuICovXG5kZWNsYXJlIGdsb2JhbCB7XG4gIGludGVyZmFjZSBXaW5kb3cge1xuICAgIE5vdGlvbldwRnJvbnRlbmQ6IEZyb250ZW5kQXBwO1xuICB9XG59XG5cbndpbmRvdy5Ob3Rpb25XcEZyb250ZW5kID0gZnJvbnRlbmRBcHA7XG5cbi8qKlxuICogRE9N5YeG5aSH5bCx57uq5ZCO5Yid5aeL5YyWXG4gKi9cbnJlYWR5KCgpID0+IHtcbiAgZnJvbnRlbmRBcHAuaW5pdCgpO1xufSk7XG5cbi8qKlxuICog5a+85Ye65Li76KaB5Yqf6IO9XG4gKi9cbmV4cG9ydCB7IGZyb250ZW5kQXBwIH07XG5leHBvcnQgZGVmYXVsdCBmcm9udGVuZEFwcDtcbiJdLCJuYW1lcyI6WyJlIiwidCIsInIiLCJTeW1ib2wiLCJuIiwiaXRlcmF0b3IiLCJvIiwidG9TdHJpbmdUYWciLCJpIiwiYyIsInByb3RvdHlwZSIsIkdlbmVyYXRvciIsInUiLCJPYmplY3QiLCJjcmVhdGUiLCJfcmVnZW5lcmF0b3JEZWZpbmUyIiwiZiIsInAiLCJ5IiwiRyIsInYiLCJhIiwiZCIsImJpbmQiLCJsZW5ndGgiLCJsIiwiVHlwZUVycm9yIiwiY2FsbCIsImRvbmUiLCJ2YWx1ZSIsInJldHVybiIsIkdlbmVyYXRvckZ1bmN0aW9uIiwiR2VuZXJhdG9yRnVuY3Rpb25Qcm90b3R5cGUiLCJnZXRQcm90b3R5cGVPZiIsInNldFByb3RvdHlwZU9mIiwiX19wcm90b19fIiwiZGlzcGxheU5hbWUiLCJfcmVnZW5lcmF0b3IiLCJ3IiwibSIsImRlZmluZVByb3BlcnR5IiwiX3JlZ2VuZXJhdG9yRGVmaW5lIiwiX2ludm9rZSIsImVudW1lcmFibGUiLCJjb25maWd1cmFibGUiLCJ3cml0YWJsZSIsImFzeW5jR2VuZXJhdG9yU3RlcCIsIlByb21pc2UiLCJyZXNvbHZlIiwidGhlbiIsIl9hc3luY1RvR2VuZXJhdG9yIiwiYXJndW1lbnRzIiwiYXBwbHkiLCJfbmV4dCIsIl90aHJvdyIsIl9jbGFzc0NhbGxDaGVjayIsIl9kZWZpbmVQcm9wZXJ0aWVzIiwiX3RvUHJvcGVydHlLZXkiLCJrZXkiLCJfY3JlYXRlQ2xhc3MiLCJfZGVmaW5lUHJvcGVydHkiLCJfdG9QcmltaXRpdmUiLCJfdHlwZW9mIiwidG9QcmltaXRpdmUiLCJTdHJpbmciLCJOdW1iZXIiLCJyZWFkeSIsImV2ZW50QnVzIiwiZW1pdCIsImZyb250ZW5kQ29udGVudCIsIkZyb250ZW5kQXBwIiwiaW5pdCIsImluaXRpYWxpemVkIiwiY29uc29sZSIsImxvZyIsImluaXRpYWxpemVDb21wb25lbnRzIiwiYmluZEV2ZW50cyIsImluaXRpYWxpemVGcm9udGVuZENvbnRlbnQiLCJpbml0Tm90aW9uQmxvY2tzIiwiaW5pdExhenlMb2FkaW5nIiwiaW5pdE1hdGhSZW5kZXJpbmciLCJub3Rpb25CbG9ja3MiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJjb25jYXQiLCJmb3JFYWNoIiwiYmxvY2siLCJibG9ja1R5cGUiLCJnZXRBdHRyaWJ1dGUiLCJ3aW5kb3ciLCJsYXp5SW1hZ2VzIiwiaW1hZ2VPYnNlcnZlciIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwiZW50cmllcyIsImVudHJ5IiwiaXNJbnRlcnNlY3RpbmciLCJpbWciLCJ0YXJnZXQiLCJzcmMiLCJyZW1vdmVBdHRyaWJ1dGUiLCJ1bm9ic2VydmUiLCJvYnNlcnZlIiwibWF0aEVsZW1lbnRzIiwibG9hZE1hdGhSZW5kZXJlciIsImVsZW1lbnQiLCJfbG9hZE1hdGhSZW5kZXJlciIsIl9jYWxsZWUiLCJfY29udGV4dCIsInNldFRpbWVvdXQiLCJvbiIsIl9ldmVudCIsIm1ldHJpY3MiLCJzY3JvbGxUaW1lb3V0IiwiYWRkRXZlbnRMaXN0ZW5lciIsImNsZWFyVGltZW91dCIsInNjcm9sbFkiLCJzY3JvbGxYIiwid2lkdGgiLCJpbm5lcldpZHRoIiwiaGVpZ2h0IiwiaW5uZXJIZWlnaHQiLCJkZXN0cm95IiwicmVtb3ZlQWxsTGlzdGVuZXJzIiwiZnJvbnRlbmRBcHAiLCJOb3Rpb25XcEZyb250ZW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/frontend/frontend.ts\n\n}");

/***/ }),

/***/ "./src/styles/frontend/frontend.scss":
/*!*******************************************!*\
  !*** ./src/styles/frontend/frontend.scss ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ \"./node_modules/style-loader/dist/runtime/styleDomAPI.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ \"./node_modules/style-loader/dist/runtime/insertBySelector.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ \"./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ \"./node_modules/style-loader/dist/runtime/insertStyleElement.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ \"./node_modules/style-loader/dist/runtime/styleTagTransform.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_2_use_2_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_2_use_3_frontend_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!../../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[2].use[3]!./frontend.scss */ \"./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[2].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[2].use[3]!./src/styles/frontend/frontend.scss\");\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());\noptions.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());\n\n      options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, \"head\");\n    \noptions.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());\noptions.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());\n\nvar update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_2_use_2_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_2_use_3_frontend_scss__WEBPACK_IMPORTED_MODULE_6__[\"default\"], options);\n\n\n\n\n       /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_2_use_2_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_2_use_3_frontend_scss__WEBPACK_IMPORTED_MODULE_6__[\"default\"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_2_use_2_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_2_use_3_frontend_scss__WEBPACK_IMPORTED_MODULE_6__[\"default\"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_2_use_2_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_2_use_3_frontend_scss__WEBPACK_IMPORTED_MODULE_6__[\"default\"].locals : undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/frontend/frontend.scss\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common"], () => (__webpack_exec__("./src/frontend/frontend.ts")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);